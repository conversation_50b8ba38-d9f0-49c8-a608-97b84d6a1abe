/**
 * 会员模拟数据
 */

import type { Member } from '@/types/member'

export const mockMembers: Member[] = [
  {
    id: 1,
    name: '张三',
    gender: 1,
    age: 45,
    phone: '13800138001',
    chiefComplaint: '高血压，头晕，偶有胸闷',
    avatar: '',
    idCard: '110101197801011234',
    emergencyContact: '李四',
    emergencyPhone: '13800138002',
    address: '北京市朝阳区建国路88号',
    healthTags: ['高血压', '心脏病'],
    status: 1,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-25T14:20:00Z'
  },
  {
    id: 2,
    name: '王芳',
    gender: 2,
    age: 38,
    phone: '13800138003',
    chiefComplaint: '糖尿病，血糖控制不稳定',
    avatar: '',
    idCard: '110101198601011234',
    emergencyContact: '王强',
    emergencyPhone: '13800138004',
    address: '北京市海淀区中关村大街123号',
    healthTags: ['糖尿病', '高血脂'],
    status: 1,
    createdAt: '2024-01-10T09:15:00Z',
    updatedAt: '2024-01-24T16:45:00Z'
  },
  {
    id: 3,
    name: '李明',
    gender: 1,
    age: 52,
    phone: '13800138005',
    chiefComplaint: '关节炎，膝盖疼痛，活动受限',
    avatar: '',
    idCard: '110101197201011234',
    emergencyContact: '李红',
    emergencyPhone: '13800138006',
    address: '北京市西城区西单北大街56号',
    healthTags: ['关节炎', '肥胖'],
    status: 1,
    createdAt: '2024-01-08T11:20:00Z',
    updatedAt: '2024-01-23T13:30:00Z'
  },
  {
    id: 4,
    name: '赵敏',
    gender: 2,
    age: 29,
    phone: '13800138007',
    chiefComplaint: '失眠，焦虑，工作压力大',
    avatar: '',
    idCard: '110101199501011234',
    emergencyContact: '赵刚',
    emergencyPhone: '13800138008',
    address: '北京市东城区王府井大街78号',
    healthTags: ['失眠', '焦虑'],
    status: 1,
    createdAt: '2024-01-20T15:45:00Z',
    updatedAt: '2024-01-25T10:15:00Z'
  },
  {
    id: 5,
    name: '陈伟',
    gender: 1,
    age: 41,
    phone: '13800138009',
    chiefComplaint: '胃病，经常胃痛，消化不良',
    avatar: '',
    idCard: '110101198301011234',
    emergencyContact: '陈丽',
    emergencyPhone: '13800138010',
    address: '北京市丰台区南三环西路99号',
    healthTags: ['胃病'],
    status: 1,
    createdAt: '2024-01-12T08:30:00Z',
    updatedAt: '2024-01-22T17:20:00Z'
  }
]

// 生成更多模拟数据
export const generateMockMembers = (count: number = 50): Member[] => {
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const surnames = ['张', '李', '王', '赵', '钱', '孙', '周', '吴', '郑', '冯', '陈', '褚', '卫', '蒋']
  const givenNames = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英']
  const healthTags = ['高血压', '糖尿病', '心脏病', '高血脂', '肥胖', '失眠', '焦虑', '关节炎', '哮喘', '胃病']
  const addresses = [
    '北京市朝阳区建国路',
    '北京市海淀区中关村大街',
    '北京市西城区西单北大街',
    '北京市东城区王府井大街',
    '北京市丰台区南三环西路'
  ]

  const members: Member[] = []

  for (let i = 1; i <= count; i++) {
    const gender = Math.random() > 0.5 ? 1 : 2
    const surname = surnames[Math.floor(Math.random() * surnames.length)]
    const givenName = givenNames[Math.floor(Math.random() * givenNames.length)]
    const name = surname + givenName
    const age = Math.floor(Math.random() * 60) + 20
    const phone = `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`
    const selectedTags = healthTags
      .sort(() => 0.5 - Math.random())
      .slice(0, Math.floor(Math.random() * 3) + 1)
    
    members.push({
      id: i,
      name,
      gender,
      age,
      phone,
      chiefComplaint: `${selectedTags.join('、')}相关症状，需要定期监测和治疗`,
      avatar: '',
      idCard: `11010119${String(1950 + age).slice(-2)}01011234`,
      emergencyContact: `${surname}${givenNames[Math.floor(Math.random() * givenNames.length)]}`,
      emergencyPhone: `139${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      address: `${addresses[Math.floor(Math.random() * addresses.length)]}${Math.floor(Math.random() * 200) + 1}号`,
      healthTags: selectedTags,
      status: Math.random() > 0.1 ? 1 : Math.random() > 0.5 ? 2 : 3,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    })
  }

  return members
}
