/**
 * 提醒模拟数据
 */

import type { WorkReminder } from '@/types/reminder'

export const mockReminders: WorkReminder[] = [
  {
    id: 1,
    butlerId: 1,
    reminderType: 1,
    title: '新处方单需要处理',
    content: '张三的高血压处方单需要立即执行，包含降压药物和生活指导',
    relatedId: 123,
    priority: 1,
    status: 0,
    isProcessed: false,
    repeatCount: 0,
    dueTime: '2024-01-25T16:00:00Z',
    createdAt: '2024-01-25T10:30:00Z',
    updatedAt: '2024-01-25T10:30:00Z'
  },
  {
    id: 2,
    butlerId: 1,
    reminderType: 2,
    title: '定期回访提醒',
    content: '李四的定期回访时间已到，需要了解用药情况和身体状况',
    relatedId: 456,
    priority: 2,
    status: 0,
    isProcessed: false,
    repeatCount: 1,
    dueTime: '2024-01-25T14:00:00Z',
    createdAt: '2024-01-25T09:00:00Z',
    updatedAt: '2024-01-25T09:00:00Z'
  },
  {
    id: 3,
    butlerId: 1,
    reminderType: 4,
    title: '健康问卷待推送',
    content: '王芳的糖尿病评估问卷需要推送，请及时发送给会员',
    relatedId: 789,
    priority: 3,
    status: 1,
    isProcessed: false,
    repeatCount: 0,
    createdAt: '2024-01-24T16:30:00Z',
    updatedAt: '2024-01-25T08:15:00Z'
  },
  {
    id: 4,
    butlerId: 1,
    reminderType: 5,
    title: '会员咨询待回复',
    content: '赵敏咨询关于失眠治疗的问题，需要专业回复',
    relatedId: 101,
    priority: 2,
    status: 0,
    isProcessed: false,
    repeatCount: 0,
    dueTime: '2024-01-25T18:00:00Z',
    createdAt: '2024-01-25T12:45:00Z',
    updatedAt: '2024-01-25T12:45:00Z'
  },
  {
    id: 5,
    butlerId: 1,
    reminderType: 6,
    title: '健康指标异常',
    content: '陈伟的血压监测数据异常，建议立即联系并安排检查',
    relatedId: 202,
    priority: 1,
    status: 0,
    isProcessed: false,
    repeatCount: 2,
    dueTime: '2024-01-25T15:30:00Z',
    createdAt: '2024-01-25T11:20:00Z',
    updatedAt: '2024-01-25T11:20:00Z'
  },
  {
    id: 6,
    butlerId: 1,
    reminderType: 3,
    title: '订单处理提醒',
    content: '新的健康产品订单需要处理，订单号：ORD20240125001',
    relatedId: 303,
    priority: 3,
    status: 1,
    isProcessed: true,
    repeatCount: 0,
    createdAt: '2024-01-24T14:20:00Z',
    updatedAt: '2024-01-25T09:30:00Z'
  }
]
