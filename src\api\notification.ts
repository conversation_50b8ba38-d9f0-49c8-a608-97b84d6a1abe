/**
 * 实时通知相关API接口
 */

import { request } from '@/utils/request'
import type {
  Notification,
  NotificationSettings,
  NotificationQuery,
  NotificationStats,
  PushMessage,
  NotificationTemplate,
  NotificationRule,
  NotificationType,
  NotificationPriority,
  PushChannel
} from '@/types/notification'
import type { ApiResponse, PaginationResult } from '@/types/api'

/**
 * 获取通知列表
 */
export const getNotifications = (params: NotificationQuery = {}) => {
  return request<PaginationResult<Notification>>({
    url: '/notifications',
    method: 'GET',
    params
  })
}

/**
 * 获取通知详情
 */
export const getNotification = (id: string) => {
  return request<Notification>({
    url: `/notifications/${id}`,
    method: 'GET'
  })
}

/**
 * 标记通知为已读
 */
export const markNotificationRead = (id: string) => {
  return request<Notification>({
    url: `/notifications/${id}/read`,
    method: 'POST'
  })
}

/**
 * 批量标记通知为已读
 */
export const markNotificationsRead = (ids: string[]) => {
  return request<void>({
    url: '/notifications/batch-read',
    method: 'POST',
    data: { ids }
  })
}

/**
 * 标记所有通知为已读
 */
export const markAllNotificationsRead = () => {
  return request<void>({
    url: '/notifications/read-all',
    method: 'POST'
  })
}

/**
 * 处理通知
 */
export const processNotification = (id: string, data?: any) => {
  return request<Notification>({
    url: `/notifications/${id}/process`,
    method: 'POST',
    data
  })
}

/**
 * 忽略通知
 */
export const dismissNotification = (id: string) => {
  return request<Notification>({
    url: `/notifications/${id}/dismiss`,
    method: 'POST'
  })
}

/**
 * 删除通知
 */
export const deleteNotification = (id: string) => {
  return request<void>({
    url: `/notifications/${id}`,
    method: 'DELETE'
  })
}

/**
 * 批量删除通知
 */
export const deleteNotifications = (ids: string[]) => {
  return request<void>({
    url: '/notifications/batch-delete',
    method: 'DELETE',
    data: { ids }
  })
}

/**
 * 获取通知统计
 */
export const getNotificationStats = () => {
  return request<NotificationStats>({
    url: '/notifications/stats',
    method: 'GET'
  })
}

/**
 * 获取未读通知数量
 */
export const getUnreadCount = () => {
  return request<{ count: number }>({
    url: '/notifications/unread-count',
    method: 'GET'
  })
}

/**
 * 创建通知
 */
export const createNotification = (data: Omit<Notification, 'id' | 'createdAt'>) => {
  return request<Notification>({
    url: '/notifications',
    method: 'POST',
    data
  })
}

/**
 * 发送推送消息
 */
export const sendPushMessage = (data: {
  recipients: string[]
  title: string
  content: string
  type: NotificationType
  priority: NotificationPriority
  channels: PushChannel[]
  relatedType?: string
  relatedId?: string
  actions?: any[]
}) => {
  return request<PushMessage[]>({
    url: '/notifications/push',
    method: 'POST',
    data
  })
}

/**
 * 获取通知设置
 */
export const getNotificationSettings = () => {
  return request<NotificationSettings>({
    url: '/notifications/settings',
    method: 'GET'
  })
}

/**
 * 更新通知设置
 */
export const updateNotificationSettings = (settings: Partial<NotificationSettings>) => {
  return request<NotificationSettings>({
    url: '/notifications/settings',
    method: 'PUT',
    data: settings
  })
}

/**
 * 测试通知推送
 */
export const testNotificationPush = (channel: PushChannel, message: {
  title: string
  content: string
}) => {
  return request<{ success: boolean; message: string }>({
    url: '/notifications/test-push',
    method: 'POST',
    data: { channel, ...message }
  })
}

/**
 * 获取通知模板列表
 */
export const getNotificationTemplates = (type?: NotificationType) => {
  return request<NotificationTemplate[]>({
    url: '/notifications/templates',
    method: 'GET',
    params: { type }
  })
}

/**
 * 创建通知模板
 */
export const createNotificationTemplate = (template: Omit<NotificationTemplate, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request<NotificationTemplate>({
    url: '/notifications/templates',
    method: 'POST',
    data: template
  })
}

/**
 * 更新通知模板
 */
export const updateNotificationTemplate = (id: string, template: Partial<NotificationTemplate>) => {
  return request<NotificationTemplate>({
    url: `/notifications/templates/${id}`,
    method: 'PUT',
    data: template
  })
}

/**
 * 删除通知模板
 */
export const deleteNotificationTemplate = (id: string) => {
  return request<void>({
    url: `/notifications/templates/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取通知规则列表
 */
export const getNotificationRules = () => {
  return request<NotificationRule[]>({
    url: '/notifications/rules',
    method: 'GET'
  })
}

/**
 * 创建通知规则
 */
export const createNotificationRule = (rule: Omit<NotificationRule, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request<NotificationRule>({
    url: '/notifications/rules',
    method: 'POST',
    data: rule
  })
}

/**
 * 更新通知规则
 */
export const updateNotificationRule = (id: string, rule: Partial<NotificationRule>) => {
  return request<NotificationRule>({
    url: `/notifications/rules/${id}`,
    method: 'PUT',
    data: rule
  })
}

/**
 * 删除通知规则
 */
export const deleteNotificationRule = (id: string) => {
  return request<void>({
    url: `/notifications/rules/${id}`,
    method: 'DELETE'
  })
}

/**
 * 启用/禁用通知规则
 */
export const toggleNotificationRule = (id: string, isActive: boolean) => {
  return request<NotificationRule>({
    url: `/notifications/rules/${id}/toggle`,
    method: 'POST',
    data: { isActive }
  })
}

/**
 * 获取推送消息历史
 */
export const getPushMessageHistory = (params: {
  page?: number
  limit?: number
  channel?: PushChannel
  status?: string
  startDate?: string
  endDate?: string
}) => {
  return request<PaginationResult<PushMessage>>({
    url: '/notifications/push-history',
    method: 'GET',
    params
  })
}

/**
 * 重新发送推送消息
 */
export const resendPushMessage = (messageId: string) => {
  return request<PushMessage>({
    url: `/notifications/push-history/${messageId}/resend`,
    method: 'POST'
  })
}
