name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run ESLint
      run: npm run lint:check
      
    - name: Run Prettier check
      run: npx prettier --check "src/**/*.{vue,ts,js}"
      
    - name: Run type checking
      run: npm run type-check
      
    - name: Check bundle size
      run: |
        npm run build
        npx bundlesize
      if: false # 需要配置 bundlesize
      
    - name: Security audit
      run: npm audit --audit-level moderate
      
    - name: Check for outdated dependencies
      run: npm outdated
      continue-on-error: true
