<template>
  <div class="visit-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>回访记录管理</h2>
        <p>管理会员回访记录和计划</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          新建回访
        </el-button>
        <el-button type="success" :icon="Location" @click="showLocationDialog = true">
          当前位置
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.todayVisits || 0 }}</div>
              <div class="stat-label">今日回访</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon overdue">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.overdue || 0 }}</div>
              <div class="stat-label">逾期回访</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.completed || 0 }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon rate">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.avgSatisfaction || 0 }}</div>
              <div class="stat-label">平均满意度</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <el-form :model="searchForm" inline>
        <el-form-item label="回访类型">
          <el-select v-model="searchForm.visitType" placeholder="全部类型" clearable>
            <el-option label="定期回访" :value="1" />
            <el-option label="紧急回访" :value="2" />
            <el-option label="专项回访" :value="3" />
            <el-option label="随访回访" :value="4" />
            <el-option label="满意度回访" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="回访状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="已计划" :value="1" />
            <el-option label="进行中" :value="2" />
            <el-option label="已完成" :value="3" />
            <el-option label="已取消" :value="4" />
            <el-option label="已逾期" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="会员姓名">
          <el-input v-model="searchForm.memberName" placeholder="请输入会员姓名" clearable />
        </el-form-item>
        <el-form-item label="回访时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 回访列表 -->
    <div class="visit-table">
      <el-table
        v-loading="loading"
        :data="visits"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="memberName" label="会员姓名" width="100" />
        <el-table-column prop="butlerName" label="管家" width="100" />
        <el-table-column label="回访类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getVisitTypeColor(row.visitType)">
              {{ getVisitTypeText(row.visitType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scheduledAt" label="计划时间" width="150">
          <template #default="{ row }">
            <span :class="{ 'text-danger': isOverdue(row) }">
              {{ formatDate(row.scheduledAt, 'YYYY-MM-DD HH:mm') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="80">
          <template #default="{ row }">
            {{ row.duration ? `${row.duration}分钟` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="satisfactionScore" label="满意度" width="100">
          <template #default="{ row }">
            <el-rate
              v-if="row.satisfactionScore"
              v-model="row.satisfactionScore"
              disabled
              size="small"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="location.address" label="回访地址" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row.id)">
              查看
            </el-button>
            <el-button 
              v-if="row.status === 1" 
              type="success" 
              size="small" 
              @click="startVisit(row.id)"
            >
              开始
            </el-button>
            <el-button 
              v-if="row.status === 2" 
              type="warning" 
              size="small" 
              @click="endVisit(row.id)"
            >
              结束
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="cancel" :disabled="row.status !== 1">取消</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建回访对话框 -->
    <VisitCreateDialog
      v-model="showCreateDialog"
      @success="handleCreateSuccess"
    />

    <!-- 位置信息对话框 -->
    <LocationDialog
      v-model="showLocationDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Location, 
  Calendar, 
  Warning, 
  Check, 
  Star, 
  ArrowDown 
} from '@element-plus/icons-vue'
import { useVisitStore } from '@/stores/visit'
import { formatDate } from '@/utils'
import VisitCreateDialog from './components/VisitCreateDialog.vue'
import LocationDialog from './components/LocationDialog.vue'
import type { Visit, VisitStatus, VisitType } from '@/types/visit'

const visitStore = useVisitStore()

// 响应式数据
const showCreateDialog = ref(false)
const showLocationDialog = ref(false)
const selectedVisits = ref<Visit[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const dateRange = ref<[string, string] | null>(null)

const searchForm = reactive({
  visitType: undefined,
  status: undefined,
  memberName: '',
  startDate: '',
  endDate: ''
})

// 计算属性
const visits = computed(() => visitStore.visits)
const loading = computed(() => visitStore.loading)
const total = computed(() => visitStore.total)
const stats = computed(() => visitStore.stats)

// 方法
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    visitType: undefined,
    status: undefined,
    memberName: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = null
  handleSearch()
}

const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.startDate = dates[0]
    searchForm.endDate = dates[1]
  } else {
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
}

const fetchData = () => {
  visitStore.fetchVisits({
    page: currentPage.value,
    limit: pageSize.value,
    ...searchForm
  })
}

const handleSelectionChange = (selection: Visit[]) => {
  selectedVisits.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchData()
}

const viewDetail = (id: number) => {
  // 跳转到详情页面
  console.log('查看详情:', id)
}

const startVisit = async (id: number) => {
  try {
    await visitStore.startVisitAction(id)
    ElMessage.success('回访已开始')
    fetchData()
  } catch (error) {
    ElMessage.error('开始回访失败')
  }
}

const endVisit = async (id: number) => {
  try {
    await visitStore.endVisitAction(id)
    ElMessage.success('回访已结束')
    fetchData()
  } catch (error) {
    ElMessage.error('结束回访失败')
  }
}

const handleCommand = async (command: string, row: Visit) => {
  switch (command) {
    case 'edit':
      // 编辑回访
      break
    case 'cancel':
      await cancelVisit(row.id)
      break
    case 'delete':
      await deleteVisit(row.id)
      break
  }
}

const cancelVisit = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要取消这个回访吗？', '确认取消', {
      type: 'warning'
    })
    
    await visitStore.updateVisitAction(id, { status: VisitStatus.CANCELLED })
    ElMessage.success('回访已取消')
    fetchData()
  } catch (error) {
    // 用户取消操作
  }
}

const deleteVisit = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个回访记录吗？删除后无法恢复。', '确认删除', {
      type: 'warning'
    })
    
    await visitStore.deleteVisitAction(id)
    ElMessage.success('回访记录已删除')
    fetchData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  fetchData()
}

// 辅助方法
const getVisitTypeColor = (type: VisitType) => {
  const colors = ['', 'primary', 'danger', 'warning', 'success', 'info']
  return colors[type] || 'primary'
}

const getVisitTypeText = (type: VisitType) => {
  const texts = ['', '定期回访', '紧急回访', '专项回访', '随访回访', '满意度回访']
  return texts[type] || '未知'
}

const getStatusColor = (status: VisitStatus) => {
  const colors = ['', 'warning', 'primary', 'success', 'info', 'danger']
  return colors[status] || 'info'
}

const getStatusText = (status: VisitStatus) => {
  const texts = ['', '已计划', '进行中', '已完成', '已取消', '已逾期']
  return texts[status] || '未知'
}

const isOverdue = (visit: Visit) => {
  const now = new Date()
  const scheduledTime = new Date(visit.scheduledAt)
  return scheduledTime < now && visit.status === VisitStatus.PLANNED
}

// 生命周期
onMounted(() => {
  fetchData()
  visitStore.fetchStats()
})
</script>

<style scoped>
.visit-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: var(--text-color-primary);
}

.header-left p {
  margin: 0;
  color: var(--text-color-secondary);
}

.header-right {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.stat-icon.today { background: var(--primary-color); }
.stat-icon.overdue { background: var(--danger-color); }
.stat-icon.completed { background: var(--success-color); }
.stat-icon.rate { background: var(--warning-color); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-secondary);
}

.search-filters {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.visit-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.text-danger {
  color: var(--danger-color);
}
</style>
