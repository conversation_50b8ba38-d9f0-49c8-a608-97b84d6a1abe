/**
 * 回访状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  Visit,
  VisitPlan,
  VisitQuery,
  VisitStats,
  VisitReminder,
  VisitTemplate,
  CreateVisitParams,
  UpdateVisitParams,
  LocationInfo,
  VisitStatus,
  VisitType
} from '@/types/visit'
import {
  getVisitList,
  getVisitDetail,
  createVisit,
  updateVisit,
  deleteVisit,
  startVisit,
  endVisit,
  getVisitStats,
  getTodayVisits,
  getOverdueVisits,
  getUpcomingVisits,
  getVisitPlans,
  createVisitPlan,
  updateVisitPlan,
  deleteVisitPlan,
  getVisitReminders,
  markReminderProcessed,
  getVisitTemplates,
  getCurrentLocation
} from '@/api/visit'

export const useVisitStore = defineStore('visit', () => {
  // 状态
  const visits = ref<Visit[]>([])
  const currentVisit = ref<Visit | null>(null)
  const visitPlans = ref<VisitPlan[]>([])
  const visitReminders = ref<VisitReminder[]>([])
  const visitTemplates = ref<VisitTemplate[]>([])
  const stats = ref<VisitStats | null>(null)
  const loading = ref(false)
  const total = ref(0)
  const query = ref<VisitQuery>({
    page: 1,
    limit: 20
  })

  // 计算属性
  const todayVisits = computed(() => 
    visits.value.filter(visit => {
      const today = new Date().toDateString()
      const visitDate = new Date(visit.scheduledAt).toDateString()
      return visitDate === today
    })
  )

  const overdueVisits = computed(() => 
    visits.value.filter(visit => {
      const now = new Date()
      const scheduledTime = new Date(visit.scheduledAt)
      return scheduledTime < now && visit.status === VisitStatus.PLANNED
    })
  )

  const upcomingVisits = computed(() => 
    visits.value.filter(visit => {
      const now = new Date()
      const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000)
      const scheduledTime = new Date(visit.scheduledAt)
      return scheduledTime >= now && scheduledTime <= threeDaysLater && visit.status === VisitStatus.PLANNED
    })
  )

  const completionRate = computed(() => {
    if (visits.value.length === 0) return 0
    const completed = visits.value.filter(v => v.status === VisitStatus.COMPLETED).length
    return Math.round((completed / visits.value.length) * 100)
  })

  const avgSatisfaction = computed(() => {
    const completedVisits = visits.value.filter(v => v.status === VisitStatus.COMPLETED && v.satisfactionScore)
    if (completedVisits.length === 0) return 0
    const total = completedVisits.reduce((sum, v) => sum + v.satisfactionScore, 0)
    return Math.round((total / completedVisits.length) * 10) / 10
  })

  // 获取回访列表
  const fetchVisits = async (params?: VisitQuery) => {
    try {
      loading.value = true
      if (params) {
        query.value = { ...query.value, ...params }
      }
      
      const response = await getVisitList(query.value)
      visits.value = response.list
      total.value = response.total
      
      return response
    } catch (error) {
      console.error('获取回访列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取回访详情
  const fetchVisitDetail = async (id: number) => {
    try {
      loading.value = true
      const visit = await getVisitDetail(id)
      currentVisit.value = visit
      return visit
    } catch (error) {
      console.error('获取回访详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建回访记录
  const createVisitAction = async (data: CreateVisitParams) => {
    try {
      const visit = await createVisit(data)
      visits.value.unshift(visit)
      total.value += 1
      return visit
    } catch (error) {
      console.error('创建回访记录失败:', error)
      throw error
    }
  }

  // 更新回访记录
  const updateVisitAction = async (id: number, data: UpdateVisitParams) => {
    try {
      const visit = await updateVisit(id, data)
      const index = visits.value.findIndex(v => v.id === id)
      if (index !== -1) {
        visits.value[index] = visit
      }
      if (currentVisit.value?.id === id) {
        currentVisit.value = visit
      }
      return visit
    } catch (error) {
      console.error('更新回访记录失败:', error)
      throw error
    }
  }

  // 删除回访记录
  const deleteVisitAction = async (id: number) => {
    try {
      await deleteVisit(id)
      const index = visits.value.findIndex(v => v.id === id)
      if (index !== -1) {
        visits.value.splice(index, 1)
        total.value -= 1
      }
      if (currentVisit.value?.id === id) {
        currentVisit.value = null
      }
    } catch (error) {
      console.error('删除回访记录失败:', error)
      throw error
    }
  }

  // 开始回访
  const startVisitAction = async (id: number) => {
    try {
      const location = await getCurrentLocation()
      const visit = await startVisit(id, location)
      
      const index = visits.value.findIndex(v => v.id === id)
      if (index !== -1) {
        visits.value[index] = visit
      }
      if (currentVisit.value?.id === id) {
        currentVisit.value = visit
      }
      
      return visit
    } catch (error) {
      console.error('开始回访失败:', error)
      throw error
    }
  }

  // 结束回访
  const endVisitAction = async (id: number) => {
    try {
      const location = await getCurrentLocation()
      const visit = await endVisit(id, location)
      
      const index = visits.value.findIndex(v => v.id === id)
      if (index !== -1) {
        visits.value[index] = visit
      }
      if (currentVisit.value?.id === id) {
        currentVisit.value = visit
      }
      
      return visit
    } catch (error) {
      console.error('结束回访失败:', error)
      throw error
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const statistics = await getVisitStats()
      stats.value = statistics
      return statistics
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw error
    }
  }

  // 获取今日回访
  const fetchTodayVisits = async () => {
    try {
      const todayList = await getTodayVisits()
      return todayList
    } catch (error) {
      console.error('获取今日回访失败:', error)
      throw error
    }
  }

  // 获取逾期回访
  const fetchOverdueVisits = async () => {
    try {
      const overdueList = await getOverdueVisits()
      return overdueList
    } catch (error) {
      console.error('获取逾期回访失败:', error)
      throw error
    }
  }

  // 获取即将到期回访
  const fetchUpcomingVisits = async (days: number = 3) => {
    try {
      const upcomingList = await getUpcomingVisits(days)
      return upcomingList
    } catch (error) {
      console.error('获取即将到期回访失败:', error)
      throw error
    }
  }

  // 获取回访计划
  const fetchVisitPlans = async (memberId?: number) => {
    try {
      const plans = await getVisitPlans(memberId)
      visitPlans.value = plans
      return plans
    } catch (error) {
      console.error('获取回访计划失败:', error)
      throw error
    }
  }

  // 创建回访计划
  const createVisitPlanAction = async (data: Omit<VisitPlan, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const plan = await createVisitPlan(data)
      visitPlans.value.unshift(plan)
      return plan
    } catch (error) {
      console.error('创建回访计划失败:', error)
      throw error
    }
  }

  // 获取回访提醒
  const fetchVisitReminders = async () => {
    try {
      const reminders = await getVisitReminders()
      visitReminders.value = reminders
      return reminders
    } catch (error) {
      console.error('获取回访提醒失败:', error)
      throw error
    }
  }

  // 获取回访模板
  const fetchVisitTemplates = async (visitType?: VisitType) => {
    try {
      const templates = await getVisitTemplates(visitType)
      visitTemplates.value = templates
      return templates
    } catch (error) {
      console.error('获取回访模板失败:', error)
      throw error
    }
  }

  // 获取当前位置
  const getCurrentLocationAction = async () => {
    try {
      const location = await getCurrentLocation()
      return location
    } catch (error) {
      console.error('获取当前位置失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    visits.value = []
    currentVisit.value = null
    visitPlans.value = []
    visitReminders.value = []
    visitTemplates.value = []
    stats.value = null
    total.value = 0
    query.value = { page: 1, limit: 20 }
  }

  return {
    // 状态
    visits,
    currentVisit,
    visitPlans,
    visitReminders,
    visitTemplates,
    stats,
    loading,
    total,
    query,
    
    // 计算属性
    todayVisits,
    overdueVisits,
    upcomingVisits,
    completionRate,
    avgSatisfaction,
    
    // 方法
    fetchVisits,
    fetchVisitDetail,
    createVisitAction,
    updateVisitAction,
    deleteVisitAction,
    startVisitAction,
    endVisitAction,
    fetchStats,
    fetchTodayVisits,
    fetchOverdueVisits,
    fetchUpcomingVisits,
    fetchVisitPlans,
    createVisitPlanAction,
    fetchVisitReminders,
    fetchVisitTemplates,
    getCurrentLocationAction,
    resetState
  }
})
