<template>
  <el-dialog
    v-model="visible"
    title="异常预警处理"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="alertInfo" class="alert-content">
      <!-- 预警信息 -->
      <div class="alert-info">
        <el-alert
          :title="alertInfo.title"
          :type="getAlertType(alertInfo.level)"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="alert-details">
              <p><strong>会员：</strong>{{ alertInfo.memberName }}</p>
              <p><strong>指标：</strong>{{ alertInfo.indicatorName }}</p>
              <p><strong>异常值：</strong>{{ alertInfo.value }} {{ alertInfo.unit }}</p>
              <p><strong>正常范围：</strong>{{ alertInfo.normalRange }}</p>
              <p><strong>异常程度：</strong>{{ getLevelText(alertInfo.level) }}</p>
              <p><strong>检测时间：</strong>{{ formatDate(alertInfo.detectedAt) }}</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 历史趋势 -->
      <div class="trend-section">
        <h4>近期趋势</h4>
        <div class="trend-chart" ref="trendChartRef" style="height: 200px;" />
      </div>

      <!-- 建议措施 -->
      <div class="suggestions-section">
        <h4>系统建议</h4>
        <div class="suggestions-list">
          <div 
            v-for="(suggestion, index) in alertInfo.suggestions" 
            :key="index"
            class="suggestion-item"
          >
            <el-icon class="suggestion-icon"><InfoFilled /></el-icon>
            <span>{{ suggestion }}</span>
          </div>
        </div>
      </div>

      <!-- 处理表单 -->
      <div class="handle-form">
        <h4>处理记录</h4>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="处理状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">已处理</el-radio>
              <el-radio :label="2">处理中</el-radio>
              <el-radio :label="3">已忽略</el-radio>
              <el-radio :label="4">需要关注</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="处理措施" prop="actions">
            <el-checkbox-group v-model="form.actions">
              <el-checkbox label="联系会员">联系会员确认情况</el-checkbox>
              <el-checkbox label="安排复查">安排重新检测</el-checkbox>
              <el-checkbox label="医生咨询">联系医生咨询</el-checkbox>
              <el-checkbox label="调整方案">调整健康管理方案</el-checkbox>
              <el-checkbox label="紧急处理">启动紧急处理流程</el-checkbox>
              <el-checkbox label="家属通知">通知家属关注</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="处理说明" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="请详细描述处理过程和结果"
            />
          </el-form-item>

          <el-form-item label="下次跟进" prop="followUpAt">
            <el-date-picker
              v-model="form.followUpAt"
              type="datetime"
              placeholder="请选择下次跟进时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="处理人员" prop="handlerIds">
            <el-select
              v-model="form.handlerIds"
              multiple
              placeholder="请选择处理人员"
              style="width: 100%"
            >
              <el-option
                v-for="staff in staffList"
                :key="staff.id"
                :label="staff.name"
                :value="staff.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级">
              <el-option label="低" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="高" :value="3" />
              <el-option label="紧急" :value="4" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="2"
              placeholder="其他需要记录的信息"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 相关记录 -->
      <div v-if="relatedRecords.length > 0" class="related-records">
        <h4>相关处理记录</h4>
        <el-timeline>
          <el-timeline-item
            v-for="record in relatedRecords"
            :key="record.id"
            :timestamp="formatDate(record.createdAt)"
            placement="top"
          >
            <div class="record-content">
              <div class="record-header">
                <span class="record-status">{{ getStatusText(record.status) }}</span>
                <span class="record-handler">处理人：{{ record.handlerName }}</span>
              </div>
              <div class="record-description">{{ record.description }}</div>
              <div v-if="record.actions.length > 0" class="record-actions">
                <el-tag
                  v-for="action in record.actions"
                  :key="action"
                  size="small"
                  style="margin-right: 5px;"
                >
                  {{ action }}
                </el-tag>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          保存处理记录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useHealthStore } from '@/stores/health'
import { formatDate } from '@/utils'
import type { 
  HealthAlert, 
  AbnormalLevel, 
  HandleAlertParams,
  AlertStatus 
} from '@/types/health'

interface Props {
  modelValue: boolean
  alertId?: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const healthStore = useHealthStore()
const formRef = ref<FormInstance>()
const trendChartRef = ref<HTMLElement>()
const loading = ref(false)
const alertInfo = ref<HealthAlert | null>(null)
const relatedRecords = ref<any[]>([])
const staffList = ref<any[]>([
  { id: 1, name: '张医生' },
  { id: 2, name: '李护士' },
  { id: 3, name: '王管家' }
])

// 表单数据
const form = reactive<HandleAlertParams>({
  status: 1 as AlertStatus,
  actions: [],
  description: '',
  followUpAt: '',
  handlerIds: [],
  priority: 2,
  notes: ''
})

// 表单验证规则
const rules: FormRules = {
  status: [
    { required: true, message: '请选择处理状态', trigger: 'change' }
  ],
  actions: [
    { required: true, message: '请选择处理措施', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入处理说明', trigger: 'blur' },
    { min: 10, message: '处理说明至少需要10个字符', trigger: 'blur' }
  ],
  handlerIds: [
    { required: true, message: '请选择处理人员', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取预警类型
const getAlertType = (level: AbnormalLevel) => {
  const types = ['success', 'warning', 'warning', 'error', 'error']
  return types[level] || 'info'
}

// 获取级别文本
const getLevelText = (level: AbnormalLevel) => {
  const texts = ['正常', '轻度异常', '中度异常', '重度异常', '危急']
  return texts[level] || '未知'
}

// 获取状态文本
const getStatusText = (status: AlertStatus) => {
  const texts = ['', '已处理', '处理中', '已忽略', '需要关注']
  return texts[status] || '未知'
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value || !alertInfo.value) return
  
  const chart = echarts.init(trendChartRef.value)
  
  // 模拟趋势数据
  const dates = []
  const values = []
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString())
    
    // 模拟数据，实际应该从API获取
    const baseValue = parseFloat(alertInfo.value.value as string) || 120
    const variation = (Math.random() - 0.5) * 20
    values.push(baseValue + variation)
  }
  
  const option = {
    title: {
      text: `${alertInfo.value.indicatorName}趋势`,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.axisValue}<br/>${data.seriesName}: ${data.value} ${alertInfo.value?.unit}`
      }
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: alertInfo.value.unit
    },
    series: [{
      name: alertInfo.value.indicatorName,
      type: 'line',
      data: values,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: '#409EFF'
      },
      markLine: {
        data: [
          { yAxis: alertInfo.value.normalMin, name: '正常下限' },
          { yAxis: alertInfo.value.normalMax, name: '正常上限' }
        ],
        lineStyle: {
          color: '#F56C6C',
          type: 'dashed'
        }
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  chart.setOption(option)
}

// 获取预警详情
const fetchAlertDetail = async () => {
  if (!props.alertId) return
  
  try {
    alertInfo.value = await healthStore.fetchHealthAlert(props.alertId)
    relatedRecords.value = await healthStore.fetchAlertHandleRecords(props.alertId)
    
    // 初始化图表
    nextTick(() => {
      initTrendChart()
    })
  } catch (error) {
    console.error('获取预警详情失败:', error)
    ElMessage.error('获取预警详情失败')
  }
}

// 提交处理记录
const handleSubmit = async () => {
  if (!formRef.value || !props.alertId) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    await healthStore.handleHealthAlertAction(props.alertId, form)
    
    ElMessage.success('处理记录保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存处理记录失败:', error)
    ElMessage.error('保存处理记录失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.assign(form, {
    status: 1 as AlertStatus,
    actions: [],
    description: '',
    followUpAt: '',
    handlerIds: [],
    priority: 2,
    notes: ''
  })
  
  alertInfo.value = null
  relatedRecords.value = []
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal && props.alertId) {
    fetchAlertDetail()
  }
})

// 监听预警ID变化
watch(() => props.alertId, (newId) => {
  if (newId && visible.value) {
    fetchAlertDetail()
  }
})
</script>

<style scoped>
.alert-content {
  max-height: 600px;
  overflow-y: auto;
}

.alert-info {
  margin-bottom: 24px;
}

.alert-details p {
  margin: 8px 0;
  font-size: 14px;
}

.trend-section,
.suggestions-section,
.handle-form,
.related-records {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.trend-section:last-child,
.suggestions-section:last-child,
.handle-form:last-child,
.related-records:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.suggestions-list {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-icon {
  color: var(--el-color-primary);
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.record-content {
  background: var(--el-bg-color-page);
  padding: 12px;
  border-radius: 6px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.record-status {
  font-weight: 600;
  color: var(--el-color-primary);
}

.record-handler {
  color: var(--el-text-color-secondary);
}

.record-description {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.record-actions {
  margin-top: 8px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
