<template>
  <div class="prescription-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>处方单管理</h2>
        <p>管理和执行医生开具的处方单</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          新建处方单
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.pending || 0 }}</div>
              <div class="stat-label">待执行</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon urgent">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.urgentCount || 0 }}</div>
              <div class="stat-label">紧急处方</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.todayCompleted || 0 }}</div>
              <div class="stat-label">今日完成</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.completionRate || 0 }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <el-form :model="searchForm" inline>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="待执行" :value="1" />
            <el-option label="执行中" :value="2" />
            <el-option label="已完成" :value="3" />
            <el-option label="已过期" :value="4" />
            <el-option label="已取消" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="searchForm.priority" placeholder="全部优先级" clearable>
            <el-option label="普通" :value="1" />
            <el-option label="一般" :value="2" />
            <el-option label="重要" :value="3" />
            <el-option label="紧急" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="会员姓名">
          <el-input v-model="searchForm.memberName" placeholder="请输入会员姓名" clearable />
        </el-form-item>
        <el-form-item label="医生姓名">
          <el-input v-model="searchForm.doctorName" placeholder="请输入医生姓名" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 处方单列表 -->
    <div class="prescription-table">
      <el-table
        v-loading="loading"
        :data="prescriptions"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="prescriptionNo" label="处方单号" width="150" />
        <el-table-column prop="memberName" label="会员姓名" width="100" />
        <el-table-column prop="doctorName" label="医生姓名" width="100" />
        <el-table-column prop="hospitalName" label="医院" width="120" />
        <el-table-column prop="diagnosis" label="诊断" width="150" show-overflow-tooltip />
        <el-table-column label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="issuedAt" label="开具时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.issuedAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="expiresAt" label="过期时间" width="120">
          <template #default="{ row }">
            <span :class="{ 'text-danger': isExpiringSoon(row.expiresAt) }">
              {{ formatDate(row.expiresAt) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row.id)">
              查看
            </el-button>
            <el-button 
              v-if="row.status === 1" 
              type="success" 
              size="small" 
              @click="executePrescription(row.id)"
            >
              执行
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="cancel" :disabled="row.status !== 1">取消</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedPrescriptions.length > 0" class="batch-actions">
        <span>已选择 {{ selectedPrescriptions.length }} 项</span>
        <el-button type="primary" @click="batchExecute">批量执行</el-button>
        <el-button @click="batchCancel">批量取消</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建处方单对话框 -->
    <PrescriptionCreateDialog
      v-model="showCreateDialog"
      @success="handleCreateSuccess"
    />

    <!-- 执行处方单对话框 -->
    <PrescriptionExecuteDialog
      v-model="showExecuteDialog"
      :prescription-id="currentPrescriptionId"
      @success="handleExecuteSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Clock, Warning, Check, TrendCharts, ArrowDown } from '@element-plus/icons-vue'
import { usePrescriptionStore } from '@/stores/prescription'
import { formatDate } from '@/utils'
import PrescriptionCreateDialog from './components/PrescriptionCreateDialog.vue'
import PrescriptionExecuteDialog from './components/PrescriptionExecuteDialog.vue'
import type { Prescription } from '@/types/prescription'

const prescriptionStore = usePrescriptionStore()

// 响应式数据
const showCreateDialog = ref(false)
const showExecuteDialog = ref(false)
const currentPrescriptionId = ref<number | null>(null)
const selectedPrescriptions = ref<Prescription[]>([])
const currentPage = ref(1)
const pageSize = ref(20)

const searchForm = reactive({
  status: undefined,
  priority: undefined,
  memberName: '',
  doctorName: ''
})

// 计算属性
const prescriptions = computed(() => prescriptionStore.prescriptions)
const loading = computed(() => prescriptionStore.loading)
const total = computed(() => prescriptionStore.total)
const stats = computed(() => prescriptionStore.stats)

// 方法
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    status: undefined,
    priority: undefined,
    memberName: '',
    doctorName: ''
  })
  handleSearch()
}

const fetchData = () => {
  prescriptionStore.fetchPrescriptions({
    page: currentPage.value,
    limit: pageSize.value,
    ...searchForm
  })
}

const handleSelectionChange = (selection: Prescription[]) => {
  selectedPrescriptions.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchData()
}

const viewDetail = (id: number) => {
  // 跳转到详情页面
  // router.push(`/prescriptions/${id}`)
}

const executePrescrip = (id: number) => {
  currentPrescriptionId.value = id
  showExecuteDialog.value = true
}

const handleCommand = async (command: string, row: Prescription) => {
  switch (command) {
    case 'edit':
      // 编辑处方单
      break
    case 'cancel':
      await cancelPrescription(row.id)
      break
    case 'delete':
      await deletePrescription(row.id)
      break
  }
}

const cancelPrescription = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要取消这个处方单吗？', '确认取消', {
      type: 'warning'
    })
    
    await prescriptionStore.updatePrescriptionAction(id, { status: 5 })
    ElMessage.success('处方单已取消')
    fetchData()
  } catch (error) {
    // 用户取消操作
  }
}

const deletePrescription = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个处方单吗？删除后无法恢复。', '确认删除', {
      type: 'warning'
    })
    
    await prescriptionStore.deletePrescriptionAction(id)
    ElMessage.success('处方单已删除')
    fetchData()
  } catch (error) {
    // 用户取消操作
  }
}

const batchExecute = async () => {
  const ids = selectedPrescriptions.value.map(p => p.id)
  await prescriptionStore.batchUpdateStatus(ids, 2) // 执行中
  ElMessage.success('批量执行成功')
  fetchData()
}

const batchCancel = async () => {
  const ids = selectedPrescriptions.value.map(p => p.id)
  await prescriptionStore.batchUpdateStatus(ids, 5) // 已取消
  ElMessage.success('批量取消成功')
  fetchData()
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  fetchData()
}

const handleExecuteSuccess = () => {
  showExecuteDialog.value = false
  fetchData()
}

// 辅助方法
const getPriorityType = (priority: number) => {
  const types = ['', 'info', '', 'warning', 'danger']
  return types[priority] || 'info'
}

const getPriorityText = (priority: number) => {
  const texts = ['', '普通', '一般', '重要', '紧急']
  return texts[priority] || '未知'
}

const getStatusType = (status: number) => {
  const types = ['', 'warning', 'primary', 'success', 'danger', 'info']
  return types[status] || 'info'
}

const getStatusText = (status: number) => {
  const texts = ['', '待执行', '执行中', '已完成', '已过期', '已取消']
  return texts[status] || '未知'
}

const isExpiringSoon = (expiresAt: string) => {
  const now = new Date()
  const expires = new Date(expiresAt)
  const diff = expires.getTime() - now.getTime()
  return diff <= 24 * 60 * 60 * 1000 // 24小时内过期
}

// 生命周期
onMounted(() => {
  fetchData()
  prescriptionStore.fetchStats()
})
</script>

<style scoped>
.prescription-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: var(--text-color-primary);
}

.header-left p {
  margin: 0;
  color: var(--text-color-secondary);
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.stat-icon.pending { background: var(--warning-color); }
.stat-icon.urgent { background: var(--danger-color); }
.stat-icon.completed { background: var(--success-color); }
.stat-icon.rate { background: var(--primary-color); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-secondary);
}

.search-filters {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.prescription-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.batch-actions {
  margin: 15px 0;
  padding: 10px;
  background: var(--bg-color-light);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.text-danger {
  color: var(--danger-color);
}
</style>
