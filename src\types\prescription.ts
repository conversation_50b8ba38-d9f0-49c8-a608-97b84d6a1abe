/**
 * 处方单相关类型定义
 */

// 处方单状态
export enum PrescriptionStatus {
  PENDING = 1,      // 待执行
  EXECUTING = 2,    // 执行中
  COMPLETED = 3,    // 已完成
  EXPIRED = 4,      // 已过期
  CANCELLED = 5     // 已取消
}

// 处方单优先级
export enum PrescriptionPriority {
  LOW = 1,          // 普通
  NORMAL = 2,       // 一般
  HIGH = 3,         // 重要
  URGENT = 4        // 紧急
}

// 药品信息
export interface Medication {
  id: number
  name: string                    // 药品名称
  specification: string           // 规格
  dosage: string                 // 用法用量
  frequency: string              // 服用频率
  duration: string               // 服用时长
  notes?: string                 // 备注
}

// 处方单基本信息
export interface Prescription {
  id: number
  prescriptionNo: string          // 处方单号
  memberId: number               // 会员ID
  memberName: string             // 会员姓名
  doctorId: number               // 医生ID
  doctorName: string             // 医生姓名
  hospitalName: string           // 医院名称
  department: string             // 科室
  diagnosis: string              // 诊断
  medications: Medication[]       // 药品列表
  status: PrescriptionStatus     // 状态
  priority: PrescriptionPriority // 优先级
  issuedAt: string              // 开具时间
  expiresAt: string             // 过期时间
  executedAt?: string           // 执行时间
  completedAt?: string          // 完成时间
  notes?: string                // 备注
  attachments?: string[]        // 附件
  createdAt: string
  updatedAt: string
}

// 处方单执行记录
export interface PrescriptionExecution {
  id: number
  prescriptionId: number         // 处方单ID
  butlerId: number              // 管家ID
  butlerName: string            // 管家姓名
  executionPlan: string         // 执行计划
  executionNotes: string        // 执行说明
  executionResult: string       // 执行结果
  executionImages?: string[]    // 执行图片
  memberFeedback?: string       // 会员反馈
  satisfactionScore?: number    // 满意度评分 (1-5)
  executedAt: string           // 执行时间
  createdAt: string
  updatedAt: string
}

// 处方单查询参数
export interface PrescriptionQuery {
  page?: number
  limit?: number
  status?: PrescriptionStatus
  priority?: PrescriptionPriority
  memberId?: number
  memberName?: string
  doctorName?: string
  hospitalName?: string
  startDate?: string
  endDate?: string
  keyword?: string
}

// 处方单统计信息
export interface PrescriptionStats {
  total: number                 // 总数
  pending: number              // 待执行
  executing: number            // 执行中
  completed: number            // 已完成
  expired: number              // 已过期
  cancelled: number            // 已取消
  urgentCount: number          // 紧急数量
  todayReceived: number        // 今日接收
  todayCompleted: number       // 今日完成
  completionRate: number       // 完成率
  avgExecutionTime: number     // 平均执行时间(小时)
}

// 处方单创建参数
export interface CreatePrescriptionParams {
  memberId: number
  doctorId: number
  doctorName: string
  hospitalName: string
  department: string
  diagnosis: string
  medications: Omit<Medication, 'id'>[]
  priority: PrescriptionPriority
  expiresAt: string
  notes?: string
  attachments?: string[]
}

// 处方单更新参数
export interface UpdatePrescriptionParams {
  status?: PrescriptionStatus
  priority?: PrescriptionPriority
  expiresAt?: string
  notes?: string
  attachments?: string[]
}

// 处方单执行参数
export interface ExecutePrescriptionParams {
  executionPlan: string
  executionNotes: string
  executionResult: string
  executionImages?: string[]
  memberFeedback?: string
  satisfactionScore?: number
}
