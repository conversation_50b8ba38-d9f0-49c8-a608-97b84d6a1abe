# 健康管家系统

基于Vue 3 + TypeScript + Element Plus的健康管家管理系统前端项目。

## 项目特性

- 🏥 **医疗健康主题**：专为健康管理服务设计的界面和交互
- 👨‍⚕️ **医生对接**：支持咨询转接、健康评估、方案执行
- 🔔 **智能提醒**：全面的工作提醒系统，自动化程度高
- 📊 **数据可视化**：健康指标图表、统计分析报表
- 📱 **响应式设计**：支持PC端和移动端访问
- 🎨 **现代化UI**：基于Element Plus的美观界面

## 技术栈

- **前端框架**：Vue 3.3+ (Composition API)
- **开发语言**：TypeScript 5.0+
- **UI组件库**：Element Plus 2.3+
- **状态管理**：Pinia 2.1+
- **路由管理**：Vue Router 4.2+
- **HTTP客户端**：Axios 1.4+
- **图表库**：ECharts 5.4+
- **构建工具**：Vite 4.4+
- **代码规范**：ESLint + TypeScript ESLint

## 项目结构

```
src/
├── api/                    # API接口
│   ├── auth.ts            # 认证相关
│   ├── member.ts          # 会员管理
│   ├── reminder.ts        # 工作提醒
│   └── request.ts         # 请求封装
├── components/            # 公共组件
│   └── ReminderPanel.vue  # 提醒面板
├── layout/               # 布局组件
│   └── Layout.vue        # 主布局
├── router/               # 路由配置
│   └── index.ts          # 路由定义
├── stores/               # 状态管理
│   ├── user.ts           # 用户状态
│   └── reminder.ts       # 提醒状态
├── types/                # 类型定义
│   ├── user.ts           # 用户类型
│   ├── member.ts         # 会员类型
│   ├── reminder.ts       # 提醒类型
│   └── index.ts          # 类型导出
├── views/                # 页面组件
│   ├── Login.vue         # 登录页
│   ├── members/          # 会员管理
│   │   └── MemberList.vue # 会员列表
│   └── ...               # 其他页面
├── App.vue               # 根组件
└── main.ts               # 入口文件
```

## 核心功能模块

### 1. 会员管理
- ✅ 会员建档（基本信息、多媒体文件）
- ✅ 健康指标录入和图表展示
- ✅ 回访记录管理
- ✅ 会员搜索和筛选

### 2. 工作提醒系统
- ✅ 多类型提醒（处方单、回访、订单、问卷、咨询）
- ✅ 优先级管理（紧急、重要、普通、信息）
- ✅ 智能提醒规则和自动触发
- ✅ 提醒设置和模板管理

### 3. 医生对接
- 🚧 咨询转接功能
- 🚧 健康评估申请
- 🚧 三方沟通平台
- 🚧 方案执行跟踪

### 4. 问卷管理
- 🚧 问卷列表和推送
- 🚧 微信公众号集成
- 🚧 填写记录和分析

### 5. 处方单管理
- 🚧 处方接收和执行
- 🚧 执行进度跟踪
- 🚧 效果反馈

### 6. 统计分析
- 🚧 工作量统计
- 🚧 数据可视化图表
- 🚧 Excel导出功能

## 快速开始

### 环境要求

- Node.js 16.0+
- npm 8.0+ 或 yarn 1.22+

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发环境运行

```bash
npm run dev
# 或
yarn dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 代码检查

```bash
npm run lint
# 或
yarn lint
```

## 界面预览

### 登录页面
- 医疗健康主题设计
- 渐变背景和毛玻璃效果
- 响应式布局

### 主工作台
- 左侧导航菜单
- 顶部用户信息和提醒
- 主内容区域

### 会员管理
- 会员列表和搜索
- 会员详情和编辑
- 健康指标图表

### 工作提醒
- 提醒面板和列表
- 优先级标识
- 快速处理操作

## API接口

项目使用RESTful API设计，主要接口包括：

- `/api/auth/*` - 认证相关
- `/api/members/*` - 会员管理
- `/api/reminders/*` - 工作提醒
- `/api/questionnaires/*` - 问卷管理
- `/api/prescriptions/*` - 处方单
- `/api/doctors/*` - 医生对接

## 开发规范

### 代码风格
- 使用TypeScript严格模式
- 遵循Vue 3 Composition API最佳实践
- 组件命名使用PascalCase
- 文件命名使用kebab-case

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 部署说明

### 环境变量配置

```bash
# .env.production
VITE_API_BASE_URL=https://api.health-butler.com
VITE_APP_TITLE=健康管家系统
```

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/health-butler;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend-server:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：<EMAIL>

---

**注意**：这是一个演示项目，实际部署时请确保配置正确的后端API地址和相关环境变量。
