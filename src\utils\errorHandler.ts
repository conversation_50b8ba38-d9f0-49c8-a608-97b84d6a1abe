/**
 * 错误处理工具
 */

import { ElMessage, ElNotification } from 'element-plus'
import type { AxiosError } from 'axios'
import { API_CODES } from '@/constants'

export interface ApiError {
  code: number
  message: string
  data?: any
}

/**
 * 处理API错误
 */
export const handleApiError = (error: AxiosError<ApiError>) => {
  const { response } = error
  
  if (!response) {
    ElMessage.error('网络连接失败，请检查网络设置')
    return
  }

  const { status, data } = response
  const message = data?.message || getDefaultErrorMessage(status)

  switch (status) {
    case API_CODES.UNAUTHORIZED:
      ElMessage.error('登录已过期，请重新登录')
      // 清除token并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
      break
      
    case API_CODES.FORBIDDEN:
      ElMessage.error('没有权限执行此操作')
      break
      
    case API_CODES.NOT_FOUND:
      ElMessage.error('请求的资源不存在')
      break
      
    case API_CODES.SERVER_ERROR:
      ElMessage.error('服务器内部错误，请稍后重试')
      break
      
    default:
      ElMessage.error(message)
  }
}

/**
 * 获取默认错误消息
 */
const getDefaultErrorMessage = (status: number): string => {
  const messages: Record<number, string> = {
    400: '请求参数错误',
    401: '未授权访问',
    403: '禁止访问',
    404: '资源不存在',
    405: '请求方法不允许',
    408: '请求超时',
    409: '资源冲突',
    422: '请求参数验证失败',
    429: '请求过于频繁',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  }
  
  return messages[status] || `请求失败 (${status})`
}

/**
 * 处理业务错误
 */
export const handleBusinessError = (error: ApiError) => {
  ElMessage.error(error.message || '操作失败')
}

/**
 * 显示成功消息
 */
export const showSuccess = (message: string) => {
  ElMessage.success(message)
}

/**
 * 显示警告消息
 */
export const showWarning = (message: string) => {
  ElMessage.warning(message)
}

/**
 * 显示信息消息
 */
export const showInfo = (message: string) => {
  ElMessage.info(message)
}

/**
 * 显示通知
 */
export const showNotification = (
  title: string,
  message: string,
  type: 'success' | 'warning' | 'info' | 'error' = 'info'
) => {
  ElNotification({
    title,
    message,
    type,
    duration: 4500
  })
}

/**
 * 全局错误处理器
 */
export const setupGlobalErrorHandler = () => {
  // 处理未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    ElMessage.error('系统出现异常，请刷新页面重试')
    event.preventDefault()
  })

  // 处理JavaScript运行时错误
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error)
    ElMessage.error('页面出现错误，请刷新页面重试')
  })

  // 处理Vue错误 - 需要在main.ts中设置
  // app.config.errorHandler = (error, instance, info) => {
  //   console.error('Vue error:', error, info)
  //   ElMessage.error('组件渲染出错，请刷新页面重试')
  // }
}

/**
 * 日志记录
 */
export const logger = {
  info: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      console.log(`[INFO] ${message}`, data)
    }
  },
  
  warn: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      console.warn(`[WARN] ${message}`, data)
    }
  },
  
  error: (message: string, error?: any) => {
    console.error(`[ERROR] ${message}`, error)
    
    // 在生产环境中可以发送错误到监控服务
    if (import.meta.env.PROD) {
      // sendErrorToMonitoring(message, error)
    }
  }
}

/**
 * 重试机制
 */
export const retry = async <T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      logger.warn(`Attempt ${attempt} failed:`, error)
      
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
  }
  
  throw lastError!
}
