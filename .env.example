# 环境变量配置示例文件
# 复制此文件为 .env.local 并修改相应配置

# 应用基本信息
VITE_APP_TITLE=健康管家系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=专业的健康管理服务平台

# API配置
VITE_API_BASE_URL=http://localhost:8080/api
VITE_API_TIMEOUT=10000

# 上传配置
VITE_UPLOAD_URL=http://localhost:8080/api/upload
VITE_UPLOAD_MAX_SIZE=10485760

# 微信公众号配置（可选）
VITE_WECHAT_APP_ID=your_wechat_app_id
VITE_WECHAT_REDIRECT_URI=http://localhost:5173/wechat/callback

# 地图服务配置（可选）
VITE_MAP_API_KEY=your_map_api_key

# 开发环境配置
VITE_DEV_MOCK=true
VITE_DEV_PROXY=true

# 生产环境配置
VITE_BUILD_GZIP=true
VITE_BUILD_ANALYZE=false
