/**
 * 文件管理相关API接口
 */

import { request } from '@/utils/request'
import type {
  FileInfo,
  FileUploadParams,
  FileQuery,
  FileStats,
  FileBatchParams,
  FileShare,
  CompressionConfig,
  WatermarkConfig,
  PreviewConfig,
  UploadProgress,
  FileValidation,
  FileProcessTask
} from '@/types/file'
import type { ApiResponse, PaginationResult } from '@/types/api'

/**
 * 上传文件
 */
export const uploadFile = (
  params: FileUploadParams,
  onProgress?: (progress: UploadProgress) => void
) => {
  const formData = new FormData()
  formData.append('file', params.file)
  
  // 添加其他参数
  if (params.category) formData.append('category', params.category)
  if (params.description) formData.append('description', params.description)
  if (params.memberId) formData.append('memberId', String(params.memberId))
  if (params.relatedType) formData.append('relatedType', params.relatedType)
  if (params.relatedId) formData.append('relatedId', String(params.relatedId))
  if (params.tags) formData.append('tags', JSON.stringify(params.tags))
  
  // 上传配置
  if (params.generateThumbnail !== undefined) {
    formData.append('generateThumbnail', String(params.generateThumbnail))
  }
  if (params.compressImage !== undefined) {
    formData.append('compressImage', String(params.compressImage))
  }
  if (params.watermark !== undefined) {
    formData.append('watermark', String(params.watermark))
  }
  
  return request<FileInfo>({
    url: '/files/upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress: UploadProgress = {
          fileId: '',
          fileName: params.file.name,
          loaded: progressEvent.loaded,
          total: progressEvent.total,
          percentage: Math.round((progressEvent.loaded / progressEvent.total) * 100)
        }
        onProgress(progress)
      }
    }
  })
}

/**
 * 批量上传文件
 */
export const uploadMultipleFiles = (
  files: File[],
  params: Omit<FileUploadParams, 'file'>,
  onProgress?: (fileIndex: number, progress: UploadProgress) => void
) => {
  const formData = new FormData()
  
  // 添加所有文件
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file)
  })
  
  // 添加其他参数
  if (params.category) formData.append('category', params.category)
  if (params.description) formData.append('description', params.description)
  if (params.memberId) formData.append('memberId', String(params.memberId))
  if (params.relatedType) formData.append('relatedType', params.relatedType)
  if (params.relatedId) formData.append('relatedId', String(params.relatedId))
  if (params.tags) formData.append('tags', JSON.stringify(params.tags))
  
  return request<FileInfo[]>({
    url: '/files/upload/batch',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取文件列表
 */
export const getFileList = (params: FileQuery = {}) => {
  return request<PaginationResult<FileInfo>>({
    url: '/files',
    method: 'GET',
    params
  })
}

/**
 * 获取文件详情
 */
export const getFileDetail = (fileId: string) => {
  return request<FileInfo>({
    url: `/files/${fileId}`,
    method: 'GET'
  })
}

/**
 * 更新文件信息
 */
export const updateFile = (fileId: string, data: Partial<FileInfo>) => {
  return request<FileInfo>({
    url: `/files/${fileId}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除文件
 */
export const deleteFile = (fileId: string) => {
  return request<void>({
    url: `/files/${fileId}`,
    method: 'DELETE'
  })
}

/**
 * 批量操作文件
 */
export const batchOperateFiles = (params: FileBatchParams) => {
  return request<void>({
    url: '/files/batch',
    method: 'POST',
    data: params
  })
}

/**
 * 获取文件统计信息
 */
export const getFileStats = () => {
  return request<FileStats>({
    url: '/files/stats',
    method: 'GET'
  })
}

/**
 * 验证文件
 */
export const validateFile = (file: File, config?: FileUploadParams) => {
  const formData = new FormData()
  formData.append('file', file)
  if (config) {
    formData.append('config', JSON.stringify(config))
  }
  
  return request<FileValidation>({
    url: '/files/validate',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 生成文件预览
 */
export const generatePreview = (fileId: string, config?: PreviewConfig) => {
  return request<{ previewUrl: string }>({
    url: `/files/${fileId}/preview`,
    method: 'POST',
    data: config
  })
}

/**
 * 压缩文件
 */
export const compressFile = (fileId: string, config: CompressionConfig) => {
  return request<FileInfo>({
    url: `/files/${fileId}/compress`,
    method: 'POST',
    data: config
  })
}

/**
 * 添加水印
 */
export const addWatermark = (fileId: string, config: WatermarkConfig) => {
  return request<FileInfo>({
    url: `/files/${fileId}/watermark`,
    method: 'POST',
    data: config
  })
}

/**
 * 创建文件分享
 */
export const createFileShare = (fileId: string, config: Partial<FileShare>) => {
  return request<FileShare>({
    url: `/files/${fileId}/share`,
    method: 'POST',
    data: config
  })
}

/**
 * 获取文件分享信息
 */
export const getFileShare = (shareId: string) => {
  return request<FileShare>({
    url: `/files/share/${shareId}`,
    method: 'GET'
  })
}

/**
 * 下载文件
 */
export const downloadFile = (fileId: string, fileName?: string) => {
  return request<Blob>({
    url: `/files/${fileId}/download`,
    method: 'GET',
    responseType: 'blob'
  }).then(blob => {
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName || 'download'
    link.click()
    window.URL.revokeObjectURL(url)
    return blob
  })
}

/**
 * 获取文件处理任务状态
 */
export const getProcessTask = (taskId: string) => {
  return request<FileProcessTask>({
    url: `/files/tasks/${taskId}`,
    method: 'GET'
  })
}

/**
 * 获取文件分类列表
 */
export const getFileCategories = () => {
  return request<string[]>({
    url: '/files/categories',
    method: 'GET'
  })
}

/**
 * 获取文件标签列表
 */
export const getFileTags = () => {
  return request<string[]>({
    url: '/files/tags',
    method: 'GET'
  })
}

/**
 * 搜索文件
 */
export const searchFiles = (keyword: string, filters?: Partial<FileQuery>) => {
  return request<PaginationResult<FileInfo>>({
    url: '/files/search',
    method: 'GET',
    params: { keyword, ...filters }
  })
}

/**
 * 获取最近上传的文件
 */
export const getRecentFiles = (limit: number = 10) => {
  return request<FileInfo[]>({
    url: '/files/recent',
    method: 'GET',
    params: { limit }
  })
}
