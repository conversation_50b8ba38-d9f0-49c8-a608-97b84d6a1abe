<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="layout-header">
      <div class="header-left">
        <img src="/logo.png" alt="Logo" class="logo" />
        <h1 class="system-title">健康管家系统</h1>
      </div>
      <div class="header-right">
        <!-- 工作提醒铃铛 -->
        <el-badge :value="reminderCount" :hidden="reminderCount === 0" class="reminder-badge">
          <el-button 
            type="text" 
            :icon="Bell" 
            @click="showReminders"
            class="reminder-btn"
          />
        </el-badge>
        
        <!-- 用户信息下拉菜单 -->
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :src="userInfo?.avatar" :size="32">
              {{ userInfo?.name?.charAt(0) }}
            </el-avatar>
            <span class="username">{{ userInfo?.name }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="settings">系统设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- 左侧菜单 -->
      <el-aside class="layout-aside" :width="isCollapse ? '64px' : '200px'">
        <div class="menu-toggle">
          <el-button 
            type="text" 
            :icon="isCollapse ? Expand : Fold" 
            @click="toggleCollapse"
          />
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>
          
          <el-sub-menu index="members">
            <template #title>
              <el-icon><User /></el-icon>
              <span>会员管理</span>
            </template>
            <el-menu-item index="/members">会员列表</el-menu-item>
            <el-menu-item index="/members/create">新建会员</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/questionnaires">
            <el-icon><Document /></el-icon>
            <template #title>问卷管理</template>
          </el-menu-item>
          
          <el-menu-item index="/prescriptions">
            <el-icon><Notebook /></el-icon>
            <template #title>处方单</template>
          </el-menu-item>
          
          <el-menu-item index="/statistics">
            <el-icon><DataAnalysis /></el-icon>
            <template #title>统计分析</template>
          </el-menu-item>
          
          <el-menu-item index="/visits">
            <el-icon><LocationInformation /></el-icon>
            <template #title>回访记录</template>
          </el-menu-item>

          <el-menu-item index="/health">
            <el-icon><Monitor /></el-icon>
            <template #title>健康指标</template>
          </el-menu-item>

          <el-menu-item index="/files">
            <el-icon><Folder /></el-icon>
            <template #title>文件管理</template>
          </el-menu-item>

          <el-sub-menu index="doctors">
            <template #title>
              <el-icon><Avatar /></el-icon>
              <span>医生对接</span>
            </template>
            <el-menu-item index="/doctors">咨询转接</el-menu-item>
            <el-menu-item index="/doctors/assessments">健康评估</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/reminders">
            <el-icon><Bell /></el-icon>
            <template #title>工作提醒</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="layout-main">
        <div class="content-wrapper">
          <router-view />
        </div>
      </el-main>
    </el-container>

    <!-- 工作提醒抽屉 -->
    <el-drawer
      v-model="reminderDrawer"
      title="工作提醒"
      direction="rtl"
      size="400px"
    >
      <ReminderPanel @close="reminderDrawer = false" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useReminderStore } from '@/stores/reminder'
import ReminderPanel from '@/components/ReminderPanel.vue'
import {
  House,
  User,
  Document,
  Notebook,
  DataAnalysis,
  LocationInformation,
  Monitor,
  Folder,
  Avatar,
  Bell,
  ArrowDown,
  Expand,
  Fold
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const reminderStore = useReminderStore()

const isCollapse = ref(false)
const reminderDrawer = ref(false)

const userInfo = computed(() => userStore.userInfo)
const reminderCount = computed(() => reminderStore.unreadCount)
const activeMenu = computed(() => route.path)

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const showReminders = () => {
  reminderDrawer.value = true
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      userStore.logoutAction()
      router.push('/login')
      break
  }
}

onMounted(() => {
  // 获取提醒数据
  reminderStore.fetchReminders()
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: linear-gradient(135deg, var(--medical-blue) 0%, var(--medical-teal) 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  height: 32px;
  width: 32px;
}

.system-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.reminder-badge {
  cursor: pointer;
}

.reminder-btn {
  color: white !important;
  font-size: 18px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.username {
  font-size: 14px;
  font-weight: 500;
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.layout-aside {
  background: white;
  border-right: 1px solid var(--border-color);
  transition: width 0.3s;
}

.menu-toggle {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-menu {
  border: none;
  height: calc(100% - 60px);
}

.layout-main {
  background: var(--bg-color);
  padding: 0;
}

.content-wrapper {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}
</style>
