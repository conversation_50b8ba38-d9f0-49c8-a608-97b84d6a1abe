<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <div class="error-icon">
        <el-icon size="60" color="#f56c6c">
          <Warning />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h3 class="error-title">出现了一些问题</h3>
        <p class="error-message">{{ errorMessage }}</p>
        
        <div class="error-actions">
          <el-button type="primary" @click="retry">
            重试
          </el-button>
          <el-button @click="goHome">
            返回首页
          </el-button>
        </div>
      </div>
    </div>
    
    <div v-if="showDetails" class="error-details">
      <el-collapse>
        <el-collapse-item title="错误详情" name="details">
          <pre class="error-stack">{{ errorStack }}</pre>
        </el-collapse-item>
      </el-collapse>
    </div>
    
    <div class="error-toggle">
      <el-button 
        type="text" 
        size="small" 
        @click="showDetails = !showDetails"
      >
        {{ showDetails ? '隐藏' : '显示' }}错误详情
      </el-button>
    </div>
  </div>
  
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { Warning } from '@element-plus/icons-vue'

const router = useRouter()

const hasError = ref(false)
const errorMessage = ref('')
const errorStack = ref('')
const showDetails = ref(false)

// 捕获子组件错误
onErrorCaptured((error: Error) => {
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorStack.value = error.stack || ''
  
  // 在开发环境下打印错误
  if (import.meta.env.DEV) {
    console.error('ErrorBoundary caught an error:', error)
  }
  
  // 阻止错误继续向上传播
  return false
})

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  errorStack.value = ''
  showDetails.value = false
}

const goHome = () => {
  router.push('/dashboard')
}
</script>

<style scoped>
.error-boundary {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #fafafa;
  border-radius: 8px;
  margin: 20px;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  margin-bottom: 20px;
}

.error-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.error-message {
  font-size: 14px;
  color: #666;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-details {
  margin-top: 30px;
  width: 100%;
  max-width: 600px;
}

.error-stack {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.error-toggle {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-actions {
    flex-direction: column;
  }
  
  .error-actions .el-button {
    width: 100%;
  }
}
</style>
