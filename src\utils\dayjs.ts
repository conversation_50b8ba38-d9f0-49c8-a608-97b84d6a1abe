/**
 * dayjs 配置和扩展
 */

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import updateLocale from 'dayjs/plugin/updateLocale'
import weekday from 'dayjs/plugin/weekday'
import isoWeek from 'dayjs/plugin/isoWeek'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import customParseFormat from 'dayjs/plugin/customParseFormat'

// 注册插件
dayjs.extend(relativeTime)
dayjs.extend(updateLocale)
dayjs.extend(weekday)
dayjs.extend(isoWeek)
dayjs.extend(weekOfYear)
dayjs.extend(advancedFormat)
dayjs.extend(customParseFormat)

// 设置中文语言
dayjs.locale('zh-cn')

// 自定义相对时间显示
dayjs.updateLocale('zh-cn', {
  relativeTime: {
    future: '%s后',
    past: '%s前',
    s: '几秒',
    m: '1分钟',
    mm: '%d分钟',
    h: '1小时',
    hh: '%d小时',
    d: '1天',
    dd: '%d天',
    M: '1个月',
    MM: '%d个月',
    y: '1年',
    yy: '%d年'
  }
})

export default dayjs
