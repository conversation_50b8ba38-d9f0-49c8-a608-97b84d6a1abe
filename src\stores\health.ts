/**
 * 健康指标状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  HealthRecord,
  HealthQuery,
  HealthStats,
  HealthTrend,
  HealthAlert,
  HealthIndicatorConfig,
  CreateHealthRecordParams,
  HealthIndicatorType,
  AbnormalLevel
} from '@/types/health'
import {
  getHealthRecords,
  getHealthRecord,
  createHealthRecord,
  batchCreateHealthRecords,
  updateHealthRecord,
  deleteHealthRecord,
  getHealthStats,
  getHealthTrend,
  getHealthAlerts,
  processHealthAlert,
  getHealthIndicatorConfigs,
  importHealthData,
  exportHealthData,
  getAbnormalRecords,
  getLatestHealthRecords,
  checkHealthAbnormal
} from '@/api/health'

export const useHealthStore = defineStore('health', () => {
  // 状态
  const healthRecords = ref<HealthRecord[]>([])
  const currentRecord = ref<HealthRecord | null>(null)
  const healthAlerts = ref<HealthAlert[]>([])
  const indicatorConfigs = ref<HealthIndicatorConfig[]>([])
  const stats = ref<HealthStats | null>(null)
  const trends = ref<Map<string, HealthTrend>>(new Map())
  const loading = ref(false)
  const total = ref(0)
  const query = ref<HealthQuery>({
    page: 1,
    limit: 20
  })

  // 计算属性
  const abnormalRecords = computed(() => 
    healthRecords.value.filter(record => record.isAbnormal)
  )

  const todayRecords = computed(() => 
    healthRecords.value.filter(record => {
      const today = new Date().toDateString()
      const recordDate = new Date(record.measuredAt).toDateString()
      return recordDate === today
    })
  )

  const criticalAlerts = computed(() => 
    healthAlerts.value.filter(alert => 
      alert.abnormalLevel === AbnormalLevel.CRITICAL && !alert.isProcessed
    )
  )

  const unprocessedAlerts = computed(() => 
    healthAlerts.value.filter(alert => !alert.isProcessed)
  )

  const abnormalRate = computed(() => {
    if (healthRecords.value.length === 0) return 0
    const abnormalCount = abnormalRecords.value.length
    return Math.round((abnormalCount / healthRecords.value.length) * 100)
  })

  // 获取健康记录列表
  const fetchHealthRecords = async (params?: HealthQuery) => {
    try {
      loading.value = true
      if (params) {
        query.value = { ...query.value, ...params }
      }
      
      const response = await getHealthRecords(query.value)
      healthRecords.value = response.list
      total.value = response.total
      
      return response
    } catch (error) {
      console.error('获取健康记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取健康记录详情
  const fetchHealthRecord = async (id: number) => {
    try {
      loading.value = true
      const record = await getHealthRecord(id)
      currentRecord.value = record
      return record
    } catch (error) {
      console.error('获取健康记录详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建健康记录
  const createHealthRecordAction = async (data: CreateHealthRecordParams) => {
    try {
      const record = await createHealthRecord(data)
      healthRecords.value.unshift(record)
      total.value += 1
      
      // 检查是否异常，如果异常则刷新预警列表
      if (record.isAbnormal) {
        await fetchHealthAlerts()
      }
      
      return record
    } catch (error) {
      console.error('创建健康记录失败:', error)
      throw error
    }
  }

  // 批量创建健康记录
  const batchCreateHealthRecordsAction = async (records: CreateHealthRecordParams[]) => {
    try {
      const createdRecords = await batchCreateHealthRecords(records)
      healthRecords.value.unshift(...createdRecords)
      total.value += createdRecords.length
      
      // 检查是否有异常记录，如果有则刷新预警列表
      const hasAbnormal = createdRecords.some(record => record.isAbnormal)
      if (hasAbnormal) {
        await fetchHealthAlerts()
      }
      
      return createdRecords
    } catch (error) {
      console.error('批量创建健康记录失败:', error)
      throw error
    }
  }

  // 更新健康记录
  const updateHealthRecordAction = async (id: number, data: Partial<CreateHealthRecordParams>) => {
    try {
      const record = await updateHealthRecord(id, data)
      const index = healthRecords.value.findIndex(r => r.id === id)
      if (index !== -1) {
        healthRecords.value[index] = record
      }
      if (currentRecord.value?.id === id) {
        currentRecord.value = record
      }
      return record
    } catch (error) {
      console.error('更新健康记录失败:', error)
      throw error
    }
  }

  // 删除健康记录
  const deleteHealthRecordAction = async (id: number) => {
    try {
      await deleteHealthRecord(id)
      const index = healthRecords.value.findIndex(r => r.id === id)
      if (index !== -1) {
        healthRecords.value.splice(index, 1)
        total.value -= 1
      }
      if (currentRecord.value?.id === id) {
        currentRecord.value = null
      }
    } catch (error) {
      console.error('删除健康记录失败:', error)
      throw error
    }
  }

  // 获取统计信息
  const fetchHealthStats = async () => {
    try {
      const statistics = await getHealthStats()
      stats.value = statistics
      return statistics
    } catch (error) {
      console.error('获取健康统计失败:', error)
      throw error
    }
  }

  // 获取健康趋势
  const fetchHealthTrend = async (memberId: number, indicatorType: HealthIndicatorType, days: number = 30) => {
    try {
      const trend = await getHealthTrend(memberId, indicatorType, days)
      const key = `${memberId}-${indicatorType}-${days}`
      trends.value.set(key, trend)
      return trend
    } catch (error) {
      console.error('获取健康趋势失败:', error)
      throw error
    }
  }

  // 获取健康预警
  const fetchHealthAlerts = async (processed?: boolean) => {
    try {
      const alerts = await getHealthAlerts(processed)
      healthAlerts.value = alerts
      return alerts
    } catch (error) {
      console.error('获取健康预警失败:', error)
      throw error
    }
  }

  // 处理健康预警
  const processHealthAlertAction = async (id: number, suggestions?: string[]) => {
    try {
      const alert = await processHealthAlert(id, suggestions)
      const index = healthAlerts.value.findIndex(a => a.id === id)
      if (index !== -1) {
        healthAlerts.value[index] = alert
      }
      return alert
    } catch (error) {
      console.error('处理健康预警失败:', error)
      throw error
    }
  }

  // 获取指标配置
  const fetchIndicatorConfigs = async () => {
    try {
      const configs = await getHealthIndicatorConfigs()
      indicatorConfigs.value = configs
      return configs
    } catch (error) {
      console.error('获取指标配置失败:', error)
      throw error
    }
  }

  // 导入健康数据
  const importHealthDataAction = async (file: File, memberId?: number, skipErrors: boolean = true) => {
    try {
      const result = await importHealthData({ file, memberId, skipErrors })
      
      // 刷新记录列表
      if (result.success > 0) {
        await fetchHealthRecords()
        await fetchHealthStats()
        
        // 如果有异常记录，刷新预警列表
        const hasAbnormal = result.records.some(record => record.isAbnormal)
        if (hasAbnormal) {
          await fetchHealthAlerts()
        }
      }
      
      return result
    } catch (error) {
      console.error('导入健康数据失败:', error)
      throw error
    }
  }

  // 导出健康数据
  const exportHealthDataAction = async (params: HealthQuery = {}) => {
    try {
      const blob = await exportHealthData(params)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `健康数据_${new Date().toISOString().split('T')[0]}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      
      return blob
    } catch (error) {
      console.error('导出健康数据失败:', error)
      throw error
    }
  }

  // 获取异常记录
  const fetchAbnormalRecords = async (params: HealthQuery = {}) => {
    try {
      const response = await getAbnormalRecords(params)
      return response
    } catch (error) {
      console.error('获取异常记录失败:', error)
      throw error
    }
  }

  // 获取最新健康记录
  const fetchLatestHealthRecords = async (memberId: number) => {
    try {
      const records = await getLatestHealthRecords(memberId)
      return records
    } catch (error) {
      console.error('获取最新健康记录失败:', error)
      throw error
    }
  }

  // 检查健康指标异常
  const checkHealthAbnormalAction = async (indicatorType: HealthIndicatorType, value: number | string) => {
    try {
      const result = await checkHealthAbnormal(indicatorType, value)
      return result
    } catch (error) {
      console.error('检查健康指标异常失败:', error)
      throw error
    }
  }

  // 获取指标配置
  const getIndicatorConfig = (type: HealthIndicatorType) => {
    return indicatorConfigs.value.find(config => config.type === type)
  }

  // 获取趋势数据
  const getTrend = (memberId: number, indicatorType: HealthIndicatorType, days: number = 30) => {
    const key = `${memberId}-${indicatorType}-${days}`
    return trends.value.get(key)
  }

  // 重置状态
  const resetState = () => {
    healthRecords.value = []
    currentRecord.value = null
    healthAlerts.value = []
    indicatorConfigs.value = []
    stats.value = null
    trends.value.clear()
    total.value = 0
    query.value = { page: 1, limit: 20 }
  }

  return {
    // 状态
    healthRecords,
    currentRecord,
    healthAlerts,
    indicatorConfigs,
    stats,
    trends,
    loading,
    total,
    query,
    
    // 计算属性
    abnormalRecords,
    todayRecords,
    criticalAlerts,
    unprocessedAlerts,
    abnormalRate,
    
    // 方法
    fetchHealthRecords,
    fetchHealthRecord,
    createHealthRecordAction,
    batchCreateHealthRecordsAction,
    updateHealthRecordAction,
    deleteHealthRecordAction,
    fetchHealthStats,
    fetchHealthTrend,
    fetchHealthAlerts,
    processHealthAlertAction,
    fetchIndicatorConfigs,
    importHealthDataAction,
    exportHealthDataAction,
    fetchAbnormalRecords,
    fetchLatestHealthRecords,
    checkHealthAbnormalAction,
    getIndicatorConfig,
    getTrend,
    resetState
  }
})
