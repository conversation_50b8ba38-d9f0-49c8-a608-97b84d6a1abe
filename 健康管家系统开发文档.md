# 健康管家系统开发文档

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2024年1月25日
- **适用范围**: 健康管家系统前端开发
- **技术栈**: Vue 3 + TypeScript + Element Plus
- **目标读者**: 前端开发工程师、架构师、项目经理

## 🎯 1. 开发概述

### 1.1 项目架构

健康管家系统采用现代化的前后端分离架构，前端基于Vue 3生态系统构建，提供管家端、后台管理端和会员端三个主要应用。

### 1.2 技术选型理由

| 技术 | 版本 | 选型理由 |
|------|------|----------|
| Vue 3 | 3.3+ | Composition API、更好的TypeScript支持、性能优化 |
| TypeScript | 5.0+ | 类型安全、代码提示、重构友好 |
| Element Plus | 2.4+ | 成熟的Vue 3组件库、丰富的组件生态 |
| Pinia | 2.1+ | Vue 3官方推荐状态管理、更好的TypeScript支持 |
| Vue Router | 4.2+ | Vue 3官方路由、支持组合式API |
| Vite | 5.0+ | 快速构建、热更新、现代化工具链 |

### 1.3 开发环境要求

- **Node.js**: >= 18.0.0
- **npm**: >= 9.0.0 或 **pnpm**: >= 8.0.0
- **IDE**: VS Code + Volar插件
- **浏览器**: Chrome >= 90, Firefox >= 88, Safari >= 14

## 🏗️ 2. 项目结构

### 2.1 目录结构

```
src/
├── api/                    # API接口层
│   ├── auth.ts            # 认证相关接口
│   ├── member.ts          # 会员管理接口
│   ├── questionnaire.ts   # 问卷管理接口
│   ├── prescription.ts    # 处方单接口
│   ├── visit.ts           # 回访记录接口
│   ├── health.ts          # 健康指标接口
│   └── file.ts            # 文件管理接口
├── components/             # 通用组件
│   ├── FileUpload.vue     # 文件上传组件
│   ├── MemberCard.vue     # 会员卡片组件
│   ├── StatCard.vue       # 统计卡片组件
│   └── SearchForm.vue     # 搜索表单组件
├── layout/                 # 布局组件
│   └── Layout.vue         # 主布局组件
├── router/                 # 路由配置
│   └── index.ts           # 路由定义
├── stores/                 # 状态管理
│   ├── auth.ts            # 认证状态
│   ├── member.ts          # 会员状态
│   ├── questionnaire.ts   # 问卷状态
│   ├── prescription.ts    # 处方单状态
│   ├── visit.ts           # 回访状态
│   ├── health.ts          # 健康指标状态
│   └── file.ts            # 文件管理状态
├── types/                  # 类型定义
│   ├── api.ts             # API通用类型
│   ├── user.ts            # 用户类型
│   ├── member.ts          # 会员类型
│   ├── questionnaire.ts   # 问卷类型
│   ├── prescription.ts    # 处方单类型
│   ├── visit.ts           # 回访记录类型
│   ├── health.ts          # 健康指标类型
│   └── file.ts            # 文件管理类型
├── utils/                  # 工具函数
│   ├── index.ts           # 通用工具
│   ├── request.ts         # HTTP请求封装
│   ├── auth.ts            # 认证工具
│   └── validation.ts      # 表单验证
├── views/                  # 页面组件
│   ├── auth/              # 认证页面
│   ├── dashboard/         # 仪表板
│   ├── members/           # 会员管理
│   ├── questionnaires/    # 问卷管理
│   ├── prescriptions/     # 处方单管理
│   ├── visits/            # 回访记录
│   ├── health/            # 健康指标
│   ├── reminders/         # 工作提醒
│   ├── statistics/        # 统计分析
│   └── files/             # 文件管理
├── assets/                 # 静态资源
├── styles/                 # 样式文件
└── main.ts                # 应用入口
```

### 2.2 核心模块说明

#### 2.2.1 API接口层 (`src/api/`)
负责与后端API的通信，每个业务模块对应一个API文件。

#### 2.2.2 状态管理 (`src/stores/`)
使用Pinia进行状态管理，每个业务模块对应一个store。

#### 2.2.3 类型定义 (`src/types/`)
TypeScript类型定义，确保类型安全。

#### 2.2.4 页面组件 (`src/views/`)
业务页面组件，按功能模块组织。

## 🔧 3. 开发规范

### 3.1 代码规范

#### 3.1.1 命名规范
- **文件命名**: 使用PascalCase (如: `MemberList.vue`)
- **组件命名**: 使用PascalCase (如: `<MemberCard />`)
- **变量命名**: 使用camelCase (如: `memberList`)
- **常量命名**: 使用UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **函数命名**: 使用camelCase (如: `fetchMemberList`)

#### 3.1.2 Vue组件规范
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 接口定义
interface Props {
  // props定义
}

interface Emits {
  // emits定义
}

// Props和Emits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const form = reactive({})

// 计算属性
const computedValue = computed(() => {})

// 方法
const handleSubmit = () => {}

// 生命周期
onMounted(() => {})
</script>

<style scoped>
/* 样式 */
</style>
```

#### 3.1.3 TypeScript规范
```typescript
// 接口定义
export interface Member {
  id: number
  name: string
  phone: string
  email?: string
  createdAt: string
}

// 类型别名
export type MemberStatus = 'active' | 'inactive' | 'pending'

// API参数类型
export interface MemberQuery {
  keyword?: string
  status?: MemberStatus
  page?: number
  limit?: number
}
```

### 3.2 Git提交规范

#### 3.2.1 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 3.2.2 Type类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 3.2.3 示例
```
feat(member): 添加会员列表搜索功能

- 添加关键词搜索
- 添加状态筛选
- 优化分页逻辑

Closes #123
```

### 3.3 代码质量

#### 3.3.1 ESLint配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    'vue/multi-word-component-names': 'off',
    'vue/no-v-html': 'warn'
  }
}
```

#### 3.3.2 Prettier配置
```json
{
  "semi": false,
  "singleQuote": true,
  "trailingComma": "none",
  "printWidth": 100,
  "tabWidth": 2
}
```

## 🚀 4. 开发流程

### 4.1 环境搭建

#### 4.1.1 克隆项目
```bash
git clone <repository-url>
cd health-butler-system
```

#### 4.1.2 安装依赖
```bash
# 使用npm
npm install

# 或使用pnpm (推荐)
pnpm install
```

#### 4.1.3 启动开发服务器
```bash
npm run dev
# 或
pnpm dev
```

#### 4.1.4 构建生产版本
```bash
npm run build
# 或
pnpm build
```

### 4.2 开发工作流

#### 4.2.1 功能开发流程
1. **创建功能分支**
   ```bash
   git checkout -b feature/member-management
   ```

2. **开发功能**
   - 创建类型定义
   - 实现API接口
   - 创建状态管理
   - 开发页面组件
   - 编写单元测试

3. **代码检查**
   ```bash
   npm run lint
   npm run type-check
   npm run test
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat(member): 添加会员管理功能"
   ```

5. **推送并创建PR**
   ```bash
   git push origin feature/member-management
   ```

#### 4.2.2 代码审查要点
- 代码规范性
- 类型安全性
- 性能优化
- 用户体验
- 测试覆盖率

### 4.3 测试策略

#### 4.3.1 单元测试
```typescript
// tests/components/MemberCard.test.ts
import { mount } from '@vue/test-utils'
import MemberCard from '@/components/MemberCard.vue'

describe('MemberCard', () => {
  it('renders member information correctly', () => {
    const member = {
      id: 1,
      name: '张三',
      phone: '13800138000'
    }
    
    const wrapper = mount(MemberCard, {
      props: { member }
    })
    
    expect(wrapper.text()).toContain('张三')
    expect(wrapper.text()).toContain('13800138000')
  })
})
```

#### 4.3.2 集成测试
```typescript
// tests/views/MemberList.test.ts
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import MemberList from '@/views/members/MemberList.vue'

describe('MemberList', () => {
  it('loads and displays member list', async () => {
    const wrapper = mount(MemberList, {
      global: {
        plugins: [createPinia()]
      }
    })
    
    // 测试列表加载
    await wrapper.vm.$nextTick()
    expect(wrapper.find('.member-list').exists()).toBe(true)
  })
})
```

## 📦 5. 构建部署

### 5.1 构建配置

#### 5.1.1 Vite配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

### 5.2 环境配置

#### 5.2.1 环境变量
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=健康管家系统(开发)

# .env.production
VITE_API_BASE_URL=https://api.health-butler.com
VITE_APP_TITLE=健康管家系统
```

### 5.3 Docker部署

#### 5.3.1 Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 5.3.2 Nginx配置
```nginx
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔍 6. 调试指南

### 6.1 开发工具

#### 6.1.1 Vue DevTools
- 安装Vue DevTools浏览器扩展
- 用于调试组件状态、事件、性能

#### 6.1.2 VS Code插件
- **Volar**: Vue 3语言支持
- **TypeScript Vue Plugin**: TypeScript支持
- **ESLint**: 代码检查
- **Prettier**: 代码格式化

### 6.2 常见问题

#### 6.2.1 类型错误
```typescript
// 问题：Property 'xxx' does not exist on type
// 解决：检查类型定义是否正确

// 错误示例
const member: Member = response.data // 类型不匹配

// 正确示例
const member: Member = response.data as Member
```

#### 6.2.2 路由问题
```typescript
// 问题：路由跳转失败
// 解决：检查路由配置和参数

// 错误示例
router.push('/member/detail') // 缺少参数

// 正确示例
router.push(`/member/detail/${memberId}`)
```

## 📚 7. 最佳实践

### 7.1 性能优化

#### 7.1.1 组件懒加载
```typescript
// router/index.ts
const routes = [
  {
    path: '/members',
    component: () => import('@/views/members/MemberList.vue')
  }
]
```

#### 7.1.2 状态管理优化
```typescript
// stores/member.ts
export const useMemberStore = defineStore('member', () => {
  const members = ref<Member[]>([])
  const loading = ref(false)
  
  // 缓存机制
  const fetchMembers = async (force = false) => {
    if (!force && members.value.length > 0) return
    
    loading.value = true
    try {
      const response = await memberApi.getList()
      members.value = response.data
    } finally {
      loading.value = false
    }
  }
  
  return { members, loading, fetchMembers }
})
```

### 7.2 用户体验

#### 7.2.1 加载状态
```vue
<template>
  <div v-loading="loading">
    <el-table :data="members">
      <!-- 表格内容 -->
    </el-table>
  </div>
</template>
```

#### 7.2.2 错误处理
```typescript
const handleError = (error: any) => {
  console.error('操作失败:', error)
  ElMessage.error(error.message || '操作失败，请重试')
}
```

### 7.3 安全考虑

#### 7.3.1 XSS防护
```vue
<template>
  <!-- 避免使用v-html -->
  <div>{{ userInput }}</div>
  
  <!-- 必须使用时进行转义 -->
  <div v-html="sanitizedHtml"></div>
</template>
```

#### 7.3.2 CSRF防护
```typescript
// utils/request.ts
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest'
```

## 🎨 8. UI/UX开发指南

### 8.1 设计系统

#### 8.1.1 色彩规范
```scss
// styles/variables.scss
:root {
  // 主色调 - 医疗健康主题
  --primary-color: #409EFF;
  --primary-light: #79BBFF;
  --primary-dark: #337ECC;

  // 功能色彩
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;

  // 中性色彩
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;

  // 背景色彩
  --bg-white: #FFFFFF;
  --bg-light: #F5F7FA;
  --bg-lighter: #FAFAFA;

  // 边框色彩
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
}
```

#### 8.1.2 字体规范
```scss
// 字体大小
--font-size-extra-large: 20px;
--font-size-large: 18px;
--font-size-medium: 16px;
--font-size-base: 14px;
--font-size-small: 13px;
--font-size-extra-small: 12px;

// 字体权重
--font-weight-bold: 700;
--font-weight-medium: 500;
--font-weight-normal: 400;
--font-weight-light: 300;
```

#### 8.1.3 间距规范
```scss
// 间距系统 (8px基准)
--spacing-xs: 4px;
--spacing-sm: 8px;
--spacing-md: 16px;
--spacing-lg: 24px;
--spacing-xl: 32px;
--spacing-xxl: 48px;
```

### 8.2 组件开发规范

#### 8.2.1 通用组件结构
```vue
<template>
  <div class="component-name" :class="componentClasses">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: 'small' | 'medium' | 'large'
  type?: 'primary' | 'success' | 'warning' | 'danger'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  type: 'primary',
  disabled: false
})

const componentClasses = computed(() => [
  `component-name--${props.size}`,
  `component-name--${props.type}`,
  {
    'component-name--disabled': props.disabled
  }
])
</script>

<style scoped>
.component-name {
  /* 基础样式 */
}

.component-name--small {
  /* 小尺寸样式 */
}

.component-name--medium {
  /* 中等尺寸样式 */
}

.component-name--large {
  /* 大尺寸样式 */
}
</style>
```

#### 8.2.2 响应式设计
```scss
// 断点定义
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}

// 使用示例
.member-card {
  width: 100%;

  @include respond-to(md) {
    width: 50%;
  }

  @include respond-to(lg) {
    width: 33.333%;
  }
}
```

### 8.3 表单开发指南

#### 8.3.1 表单验证
```vue
<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
  >
    <el-form-item label="会员姓名" prop="name">
      <el-input v-model="form.name" placeholder="请输入会员姓名" />
    </el-form-item>

    <el-form-item label="手机号" prop="phone">
      <el-input v-model="form.phone" placeholder="请输入手机号" />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { validatePhone } from '@/utils/validation'

const formRef = ref<FormInstance>()
const form = reactive({
  name: '',
  phone: ''
})

const rules: FormRules = {
  name: [
    { required: true, message: '请输入会员姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2到20个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    // 提交表单
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>
```

#### 8.3.2 表单工具函数
```typescript
// utils/validation.ts
export const validatePhone = (rule: any, value: string, callback: Function) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}

export const validateEmail = (rule: any, value: string, callback: Function) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (value && !emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱地址'))
  } else {
    callback()
  }
}
```

## 🔌 9. API集成指南

### 9.1 HTTP客户端配置

#### 9.1.1 Axios封装
```typescript
// utils/request.ts
import axios, { type AxiosResponse, type AxiosError } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message, data } = response.data

    if (code === 200) {
      return data
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error: AxiosError) => {
    const { response } = error

    if (response?.status === 401) {
      // 未授权，跳转登录
      const authStore = useAuthStore()
      authStore.logout()
    } else if (response?.status === 403) {
      ElMessage.error('权限不足')
    } else if (response?.status >= 500) {
      ElMessage.error('服务器错误，请稍后重试')
    } else {
      ElMessage.error(error.message || '网络错误')
    }

    return Promise.reject(error)
  }
)

export default request
```

#### 9.1.2 API接口定义
```typescript
// api/member.ts
import request from '@/utils/request'
import type { Member, MemberQuery, CreateMemberParams } from '@/types/member'
import type { ApiResponse, PaginationResponse } from '@/types/api'

export const memberApi = {
  // 获取会员列表
  getList: (params: MemberQuery): Promise<PaginationResponse<Member>> => {
    return request.get('/members', { params })
  },

  // 获取会员详情
  getDetail: (id: number): Promise<Member> => {
    return request.get(`/members/${id}`)
  },

  // 创建会员
  create: (data: CreateMemberParams): Promise<Member> => {
    return request.post('/members', data)
  },

  // 更新会员
  update: (id: number, data: Partial<Member>): Promise<Member> => {
    return request.put(`/members/${id}`, data)
  },

  // 删除会员
  delete: (id: number): Promise<void> => {
    return request.delete(`/members/${id}`)
  },

  // 搜索会员
  search: (keyword: string): Promise<Member[]> => {
    return request.get('/members/search', { params: { keyword } })
  }
}
```

### 9.2 状态管理集成

#### 9.2.1 Store定义
```typescript
// stores/member.ts
import { defineStore } from 'pinia'
import { memberApi } from '@/api/member'
import type { Member, MemberQuery } from '@/types/member'

export const useMemberStore = defineStore('member', () => {
  // 状态
  const members = ref<Member[]>([])
  const currentMember = ref<Member | null>(null)
  const loading = ref(false)
  const total = ref(0)

  // 获取会员列表
  const fetchMembers = async (query: MemberQuery = {}) => {
    try {
      loading.value = true
      const response = await memberApi.getList(query)
      members.value = response.list
      total.value = response.total
    } catch (error) {
      console.error('获取会员列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取会员详情
  const fetchMemberDetail = async (id: number) => {
    try {
      loading.value = true
      currentMember.value = await memberApi.getDetail(id)
      return currentMember.value
    } catch (error) {
      console.error('获取会员详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建会员
  const createMemberAction = async (data: CreateMemberParams) => {
    try {
      const newMember = await memberApi.create(data)
      members.value.unshift(newMember)
      return newMember
    } catch (error) {
      console.error('创建会员失败:', error)
      throw error
    }
  }

  // 更新会员
  const updateMemberAction = async (id: number, data: Partial<Member>) => {
    try {
      const updatedMember = await memberApi.update(id, data)
      const index = members.value.findIndex(m => m.id === id)
      if (index > -1) {
        members.value[index] = updatedMember
      }
      if (currentMember.value?.id === id) {
        currentMember.value = updatedMember
      }
      return updatedMember
    } catch (error) {
      console.error('更新会员失败:', error)
      throw error
    }
  }

  // 删除会员
  const deleteMemberAction = async (id: number) => {
    try {
      await memberApi.delete(id)
      const index = members.value.findIndex(m => m.id === id)
      if (index > -1) {
        members.value.splice(index, 1)
      }
      if (currentMember.value?.id === id) {
        currentMember.value = null
      }
    } catch (error) {
      console.error('删除会员失败:', error)
      throw error
    }
  }

  return {
    // 状态
    members: readonly(members),
    currentMember: readonly(currentMember),
    loading: readonly(loading),
    total: readonly(total),

    // 方法
    fetchMembers,
    fetchMemberDetail,
    createMemberAction,
    updateMemberAction,
    deleteMemberAction
  }
})
```

## 🧪 10. 测试开发指南

### 10.1 测试环境配置

#### 10.1.1 Vitest配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts']
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})
```

#### 10.1.2 测试工具配置
```typescript
// tests/setup.ts
import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'

// 全局配置
config.global.plugins = [createPinia(), ElementPlus]

// Mock API
global.fetch = vi.fn()

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
  }
})
```

### 10.2 组件测试

#### 10.2.1 基础组件测试
```typescript
// tests/components/MemberCard.test.ts
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import MemberCard from '@/components/MemberCard.vue'
import type { Member } from '@/types/member'

describe('MemberCard', () => {
  const mockMember: Member = {
    id: 1,
    name: '张三',
    phone: '13800138000',
    email: '<EMAIL>',
    age: 30,
    gender: 'male',
    createdAt: '2024-01-01T00:00:00Z'
  }

  it('renders member information correctly', () => {
    const wrapper = mount(MemberCard, {
      props: { member: mockMember }
    })

    expect(wrapper.text()).toContain('张三')
    expect(wrapper.text()).toContain('13800138000')
    expect(wrapper.text()).toContain('<EMAIL>')
  })

  it('emits edit event when edit button is clicked', async () => {
    const wrapper = mount(MemberCard, {
      props: { member: mockMember }
    })

    await wrapper.find('.edit-button').trigger('click')
    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')?.[0]).toEqual([mockMember])
  })

  it('shows placeholder when member data is missing', () => {
    const incompleteMember = { ...mockMember, email: undefined }
    const wrapper = mount(MemberCard, {
      props: { member: incompleteMember }
    })

    expect(wrapper.text()).toContain('未设置邮箱')
  })
})
```

#### 10.2.2 页面组件测试
```typescript
// tests/views/MemberList.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import MemberList from '@/views/members/MemberList.vue'
import { useMemberStore } from '@/stores/member'

// Mock API
vi.mock('@/api/member', () => ({
  memberApi: {
    getList: vi.fn().mockResolvedValue({
      list: [
        { id: 1, name: '张三', phone: '13800138000' },
        { id: 2, name: '李四', phone: '13800138001' }
      ],
      total: 2
    })
  }
}))

describe('MemberList', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('loads and displays member list on mount', async () => {
    const wrapper = mount(MemberList)
    const memberStore = useMemberStore()

    // 等待组件挂载和数据加载
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    expect(memberStore.members).toHaveLength(2)
    expect(wrapper.text()).toContain('张三')
    expect(wrapper.text()).toContain('李四')
  })

  it('filters members when search is performed', async () => {
    const wrapper = mount(MemberList)

    // 模拟搜索
    const searchInput = wrapper.find('input[placeholder="请输入会员姓名"]')
    await searchInput.setValue('张三')
    await wrapper.find('.search-button').trigger('click')

    // 验证搜索结果
    expect(wrapper.text()).toContain('张三')
    expect(wrapper.text()).not.toContain('李四')
  })
})
```

### 10.3 Store测试

#### 10.3.1 状态管理测试
```typescript
// tests/stores/member.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useMemberStore } from '@/stores/member'
import { memberApi } from '@/api/member'

// Mock API
vi.mock('@/api/member')

describe('Member Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('fetches members successfully', async () => {
    const mockMembers = [
      { id: 1, name: '张三', phone: '13800138000' },
      { id: 2, name: '李四', phone: '13800138001' }
    ]

    vi.mocked(memberApi.getList).mockResolvedValue({
      list: mockMembers,
      total: 2
    })

    const store = useMemberStore()
    await store.fetchMembers()

    expect(store.members).toEqual(mockMembers)
    expect(store.total).toBe(2)
    expect(memberApi.getList).toHaveBeenCalledOnce()
  })

  it('handles fetch error gracefully', async () => {
    const error = new Error('Network error')
    vi.mocked(memberApi.getList).mockRejectedValue(error)

    const store = useMemberStore()

    await expect(store.fetchMembers()).rejects.toThrow('Network error')
    expect(store.members).toEqual([])
    expect(store.loading).toBe(false)
  })

  it('creates member successfully', async () => {
    const newMember = { id: 3, name: '王五', phone: '13800138002' }
    vi.mocked(memberApi.create).mockResolvedValue(newMember)

    const store = useMemberStore()
    const result = await store.createMemberAction({
      name: '王五',
      phone: '13800138002'
    })

    expect(result).toEqual(newMember)
    expect(store.members[0]).toEqual(newMember)
  })
})
```

---

**文档维护**: 本文档将随着项目发展持续更新
**反馈渠道**: 如有问题或建议，请提交Issue或联系开发团队
**最后更新**: 2024年1月25日
