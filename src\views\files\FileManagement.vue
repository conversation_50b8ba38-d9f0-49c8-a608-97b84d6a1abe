<template>
  <div class="file-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>文件管理</h2>
        <p>管理系统中的所有多媒体文件</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Upload" @click="showUploadDialog = true">
          上传文件
        </el-button>
        <el-button :icon="Refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon image-icon">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats?.imageCount || 0 }}</div>
                <div class="stat-label">图片文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon video-icon">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats?.videoCount || 0 }}</div>
                <div class="stat-label">视频文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon audio-icon">
                <el-icon><Headphone /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats?.audioCount || 0 }}</div>
                <div class="stat-label">音频文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon document-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats?.documentCount || 0 }}</div>
                <div class="stat-label">文档文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="文件名">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入文件名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="文件类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option label="图片" value="image" />
              <el-option label="视频" value="video" />
              <el-option label="音频" value="audio" />
              <el-option label="文档" value="document" />
            </el-select>
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="会员">
            <el-input
              v-model="searchForm.memberName"
              placeholder="请输入会员姓名"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="上传时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
        <el-button
          v-if="selectedFiles.length > 0"
          type="danger"
          size="small"
          @click="batchDelete"
        >
          批量删除 ({{ selectedFiles.length }})
        </el-button>
        <el-button
          v-if="selectedFiles.length > 0"
          type="primary"
          size="small"
          @click="batchDownload"
        >
          批量下载
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="grid">网格视图</el-radio-button>
          <el-radio-button label="list">列表视图</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="file-list">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="grid-view">
        <el-row :gutter="20">
          <el-col
            v-for="file in files"
            :key="file.id"
            :span="6"
            class="file-col"
          >
            <el-card class="file-card" :class="{ selected: selectedFiles.includes(file.id) }">
              <div class="file-checkbox">
                <el-checkbox
                  :model-value="selectedFiles.includes(file.id)"
                  @change="handleFileSelect(file.id, $event)"
                />
              </div>
              
              <div class="file-preview" @click="previewFile(file)">
                <el-image
                  v-if="file.type === 'image' && file.thumbnailUrl"
                  :src="file.thumbnailUrl"
                  fit="cover"
                  class="preview-image"
                />
                <div v-else class="file-icon">
                  <el-icon><component :is="getFileIcon(file.type)" /></el-icon>
                </div>
              </div>
              
              <div class="file-info">
                <div class="file-name" :title="file.originalName">
                  {{ file.originalName }}
                </div>
                <div class="file-meta">
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <span class="file-date">{{ formatDate(file.createdAt, 'MM-DD') }}</span>
                </div>
                <div v-if="file.memberName" class="file-member">
                  {{ file.memberName }}
                </div>
              </div>
              
              <div class="file-actions">
                <el-button type="text" size="small" @click="previewFile(file)">
                  预览
                </el-button>
                <el-button type="text" size="small" @click="downloadFile(file)">
                  下载
                </el-button>
                <el-button type="text" size="small" @click="editFile(file)">
                  编辑
                </el-button>
                <el-button type="text" size="small" @click="deleteFile(file)">
                  删除
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view">
        <el-table
          :data="files"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="文件名" min-width="200">
            <template #default="{ row }">
              <div class="file-name-cell">
                <div class="file-icon-small">
                  <el-icon><component :is="getFileIcon(row.type)" /></el-icon>
                </div>
                <span class="file-name" :title="row.originalName">
                  {{ row.originalName }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="80">
            <template #default="{ row }">
              <el-tag size="small">{{ getTypeText(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100">
            <template #default="{ row }">
              {{ formatFileSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="memberName" label="关联会员" width="120" />
          <el-table-column prop="createdAt" label="上传时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="previewFile(row)">
                预览
              </el-button>
              <el-button type="text" size="small" @click="downloadFile(row)">
                下载
              </el-button>
              <el-button type="text" size="small" @click="editFile(row)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click="deleteFile(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文件" width="600px">
      <FileUpload
        :multiple="true"
        :limit="10"
        :max-size="100 * 1024 * 1024"
        :show-preview="true"
        @success="handleUploadSuccess"
      />
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog v-model="showPreviewDialog" :title="currentFile?.originalName" width="80%">
      <div v-if="currentFile" class="file-preview-content">
        <el-image
          v-if="currentFile.type === 'image'"
          :src="currentFile.url"
          fit="contain"
          style="width: 100%; max-height: 600px;"
        />
        <video
          v-else-if="currentFile.type === 'video'"
          :src="currentFile.url"
          controls
          style="width: 100%; max-height: 600px;"
        />
        <audio
          v-else-if="currentFile.type === 'audio'"
          :src="currentFile.url"
          controls
          style="width: 100%;"
        />
        <div v-else class="unsupported-preview">
          <el-icon size="64"><Document /></el-icon>
          <p>此文件类型不支持预览</p>
          <el-button type="primary" @click="downloadFile(currentFile)">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  Refresh,
  Picture,
  VideoPlay,
  Headphone,
  Document
} from '@element-plus/icons-vue'
import { useFileStore } from '@/stores/file'
import FileUpload from '@/components/FileUpload.vue'
import { formatFileSize, formatDate } from '@/utils'
import type { FileInfo, FileType, FileQuery } from '@/types/file'

const fileStore = useFileStore()

// 响应式数据
const loading = ref(false)
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const currentFile = ref<FileInfo | null>(null)
const viewMode = ref<'grid' | 'list'>('grid')
const selectAll = ref(false)
const selectedFiles = ref<string[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const dateRange = ref<[string, string] | null>(null)

// 搜索表单
const searchForm = reactive<FileQuery>({
  keyword: '',
  type: undefined,
  category: '',
  memberName: '',
  startDate: '',
  endDate: ''
})

// 计算属性
const files = computed(() => fileStore.files)
const total = computed(() => fileStore.total)
const stats = computed(() => fileStore.stats)
const categories = computed(() => fileStore.categories)

// 获取文件图标
const getFileIcon = (type: FileType) => {
  const icons = {
    image: Picture,
    video: VideoPlay,
    audio: Headphone,
    document: Document
  }
  return icons[type] || Document
}

// 获取类型文本
const getTypeText = (type: FileType) => {
  const texts = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档'
  }
  return texts[type] || type
}

// 搜索
const handleSearch = () => {
  if (dateRange.value) {
    searchForm.startDate = dateRange.value[0]
    searchForm.endDate = dateRange.value[1]
  }
  
  currentPage.value = 1
  fetchFiles()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    type: undefined,
    category: '',
    memberName: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = null
  currentPage.value = 1
  fetchFiles()
}

// 获取文件列表
const fetchFiles = async () => {
  try {
    loading.value = true
    await fileStore.fetchFiles({
      ...searchForm,
      page: currentPage.value,
      limit: pageSize.value
    })
  } catch (error) {
    console.error('获取文件列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchFiles()
  fileStore.fetchStats()
  fileStore.fetchCategories()
}

// 文件选择
const handleFileSelect = (fileId: string, checked: boolean) => {
  if (checked) {
    selectedFiles.value.push(fileId)
  } else {
    const index = selectedFiles.value.indexOf(fileId)
    if (index > -1) {
      selectedFiles.value.splice(index, 1)
    }
  }
}

// 全选
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedFiles.value = files.value.map(file => file.id)
  } else {
    selectedFiles.value = []
  }
}

// 表格选择变化
const handleSelectionChange = (selection: FileInfo[]) => {
  selectedFiles.value = selection.map(file => file.id)
}

// 预览文件
const previewFile = (file: FileInfo) => {
  currentFile.value = file
  showPreviewDialog.value = true
}

// 下载文件
const downloadFile = async (file: FileInfo) => {
  try {
    await fileStore.downloadFileAction(file.id, file.originalName)
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

// 编辑文件
const editFile = (file: FileInfo) => {
  ElMessage.info('编辑功能开发中')
}

// 删除文件
const deleteFile = async (file: FileInfo) => {
  try {
    await ElMessageBox.confirm(`确定要删除文件 "${file.originalName}" 吗？`, '确认删除', {
      type: 'warning'
    })
    
    await fileStore.deleteFileAction(file.id)
    ElMessage.success('删除成功')
    fetchFiles()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 批量删除
const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`, '确认删除', {
      type: 'warning'
    })
    
    await fileStore.batchOperateFilesAction({
      fileIds: selectedFiles.value,
      action: 'delete'
    })
    
    ElMessage.success('批量删除成功')
    selectedFiles.value = []
    selectAll.value = false
    fetchFiles()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 批量下载
const batchDownload = () => {
  ElMessage.info('批量下载功能开发中')
}

// 分页变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchFiles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchFiles()
}

// 上传成功
const handleUploadSuccess = () => {
  showUploadDialog.value = false
  fetchFiles()
  fileStore.fetchStats()
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.file-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: var(--el-text-color-primary);
}

.header-left p {
  margin: 0;
  color: var(--el-text-color-secondary);
}

.header-right {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.image-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.video-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.audio-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.document-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.search-section {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-list {
  margin-bottom: 20px;
}

.grid-view .file-col {
  margin-bottom: 20px;
}

.file-card {
  position: relative;
  height: 280px;
  cursor: pointer;
  transition: all 0.3s;
}

.file-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-card.selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.file-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

.file-preview {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page);
  margin-bottom: 12px;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.file-icon {
  font-size: 48px;
  color: var(--el-text-color-placeholder);
}

.file-info {
  padding: 0 16px;
  margin-bottom: 12px;
}

.file-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.file-member {
  font-size: 12px;
  color: var(--el-color-primary);
}

.file-actions {
  display: flex;
  justify-content: space-around;
  padding: 0 16px;
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon-small {
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.file-preview-content {
  text-align: center;
}

.unsupported-preview {
  padding: 60px 0;
  color: var(--el-text-color-secondary);
}

.unsupported-preview p {
  margin: 16px 0;
  font-size: 16px;
}
</style>
