// 用户相关类型定义

export interface LoginForm {
  account: string
  password: string
  remember?: boolean
}

export interface RegisterForm {
  name: string
  gender: number // 1-男 2-女
  age: number
  phone: string
  account: string
  password: string
  confirmPassword: string
  avatar?: string
}

export interface UserInfo {
  id: number
  name: string
  gender: number
  age: number
  phone: string
  account: string
  avatar?: string
  status: number // 1-正常 2-暂停 3-注销
  qrCode?: string
  createdAt: string
  updatedAt: string
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface PaginationResponse<T = any> {
  list: T[]
  total: number
  page: number
  limit: number
}
