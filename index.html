<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>俄罗斯方块</h1>
            <div class="game-info">
                <div class="score-info">
                    <div>得分: <span id="score">0</span></div>
                    <div>等级: <span id="level">1</span></div>
                    <div>行数: <span id="lines">0</span></div>
                </div>
                <div class="next-piece">
                    <h3>下一个:</h3>
                    <canvas id="nextCanvas" width="80" height="80"></canvas>
                </div>
            </div>
        </div>
        
        <div class="game-area">
            <canvas id="gameCanvas" width="300" height="600"></canvas>
        </div>
        
        <div class="game-controls">
            <div class="control-buttons">
                <button id="startBtn">开始游戏</button>
                <button id="pauseBtn">暂停</button>
                <button id="resetBtn">重新开始</button>
            </div>
            <div class="instructions">
                <h3>操作说明:</h3>
                <p>← → : 左右移动</p>
                <p>↓ : 快速下降</p>
                <p>↑ : 旋转方块</p>
                <p>空格 : 瞬间下降</p>
            </div>
        </div>
        
        <div class="game-over" id="gameOver" style="display: none;">
            <h2>游戏结束!</h2>
            <p>最终得分: <span id="finalScore">0</span></p>
            <button onclick="resetGame()">再来一局</button>
        </div>
    </div>
    
    <script src="tetris.js"></script>
</body>
</html>
