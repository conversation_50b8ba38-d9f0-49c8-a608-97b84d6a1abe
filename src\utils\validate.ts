/**
 * 验证工具函数
 */

import { REGEX } from '@/constants'

/**
 * 验证手机号
 */
export const validatePhone = (phone: string): boolean => {
  return REGEX.PHONE.test(phone)
}

/**
 * 验证邮箱
 */
export const validateEmail = (email: string): boolean => {
  return REGEX.EMAIL.test(email)
}

/**
 * 验证身份证号
 */
export const validateIdCard = (idCard: string): boolean => {
  return REGEX.ID_CARD.test(idCard)
}

/**
 * 验证密码强度
 */
export const validatePassword = (password: string): {
  isValid: boolean
  strength: 'weak' | 'medium' | 'strong'
  message: string
} => {
  if (password.length < 6) {
    return {
      isValid: false,
      strength: 'weak',
      message: '密码长度至少6位'
    }
  }

  if (password.length < 8) {
    return {
      isValid: true,
      strength: 'weak',
      message: '密码强度较弱'
    }
  }

  const hasLower = /[a-z]/.test(password)
  const hasUpper = /[A-Z]/.test(password)
  const hasNumber = /\d/.test(password)
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  const score = [hasLower, hasUpper, hasNumber, hasSpecial].filter(Boolean).length

  if (score >= 3) {
    return {
      isValid: true,
      strength: 'strong',
      message: '密码强度很强'
    }
  } else if (score >= 2) {
    return {
      isValid: true,
      strength: 'medium',
      message: '密码强度中等'
    }
  } else {
    return {
      isValid: true,
      strength: 'weak',
      message: '密码强度较弱'
    }
  }
}

/**
 * 验证年龄
 */
export const validateAge = (age: number): boolean => {
  return age >= 1 && age <= 120
}

/**
 * 验证中文姓名
 */
export const validateChineseName = (name: string): boolean => {
  const chineseNameRegex = /^[\u4e00-\u9fa5]{2,10}$/
  return chineseNameRegex.test(name)
}

/**
 * 验证URL
 */
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 验证IP地址
 */
export const validateIP = (ip: string): boolean => {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipRegex.test(ip)
}

/**
 * 验证银行卡号
 */
export const validateBankCard = (cardNumber: string): boolean => {
  const bankCardRegex = /^[1-9]\d{12,18}$/
  return bankCardRegex.test(cardNumber)
}

/**
 * 验证车牌号
 */
export const validateLicensePlate = (plate: string): boolean => {
  const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$/
  return plateRegex.test(plate)
}

/**
 * 验证QQ号
 */
export const validateQQ = (qq: string): boolean => {
  const qqRegex = /^[1-9][0-9]{4,10}$/
  return qqRegex.test(qq)
}

/**
 * 验证微信号
 */
export const validateWeChat = (wechat: string): boolean => {
  const wechatRegex = /^[a-zA-Z][-_a-zA-Z0-9]{5,19}$/
  return wechatRegex.test(wechat)
}

/**
 * 验证文件大小
 */
export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize
}

/**
 * 验证文件类型
 */
export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  return allowedTypes.includes(fileExtension || '')
}

/**
 * 验证图片文件
 */
export const validateImageFile = (file: File): {
  isValid: boolean
  message: string
} => {
  const allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  const maxSize = 10 * 1024 * 1024 // 10MB

  if (!validateFileType(file, allowedTypes)) {
    return {
      isValid: false,
      message: '只支持 JPG、PNG、GIF、BMP、WebP 格式的图片'
    }
  }

  if (!validateFileSize(file, maxSize)) {
    return {
      isValid: false,
      message: '图片大小不能超过 10MB'
    }
  }

  return {
    isValid: true,
    message: '图片格式正确'
  }
}

/**
 * 验证视频文件
 */
export const validateVideoFile = (file: File): {
  isValid: boolean
  message: string
} => {
  const allowedTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
  const maxSize = 100 * 1024 * 1024 // 100MB

  if (!validateFileType(file, allowedTypes)) {
    return {
      isValid: false,
      message: '只支持 MP4、AVI、MOV、WMV、FLV、MKV 格式的视频'
    }
  }

  if (!validateFileSize(file, maxSize)) {
    return {
      isValid: false,
      message: '视频大小不能超过 100MB'
    }
  }

  return {
    isValid: true,
    message: '视频格式正确'
  }
}

/**
 * 验证表单字段
 */
export const validateField = (
  value: any,
  rules: Array<{
    required?: boolean
    min?: number
    max?: number
    pattern?: RegExp
    validator?: (value: any) => boolean
    message: string
  }>
): {
  isValid: boolean
  message: string
} => {
  for (const rule of rules) {
    // 必填验证
    if (rule.required && (!value || value.toString().trim() === '')) {
      return {
        isValid: false,
        message: rule.message
      }
    }

    // 如果值为空且不是必填，跳过其他验证
    if (!value || value.toString().trim() === '') {
      continue
    }

    // 最小长度验证
    if (rule.min !== undefined && value.toString().length < rule.min) {
      return {
        isValid: false,
        message: rule.message
      }
    }

    // 最大长度验证
    if (rule.max !== undefined && value.toString().length > rule.max) {
      return {
        isValid: false,
        message: rule.message
      }
    }

    // 正则验证
    if (rule.pattern && !rule.pattern.test(value.toString())) {
      return {
        isValid: false,
        message: rule.message
      }
    }

    // 自定义验证器
    if (rule.validator && !rule.validator(value)) {
      return {
        isValid: false,
        message: rule.message
      }
    }
  }

  return {
    isValid: true,
    message: '验证通过'
  }
}
