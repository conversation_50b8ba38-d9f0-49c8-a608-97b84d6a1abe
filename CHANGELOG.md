# 更新日志

所有重要的项目变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划新增
- 健康指标图表可视化
- 问卷创建和编辑功能
- 医生对接模块
- 处方单管理系统
- 微信公众号集成

### 计划改进
- 移动端响应式优化
- 性能优化和代码分割
- 国际化支持
- 单元测试覆盖

## [1.0.0] - 2024-01-25

### 新增
- 🎉 项目初始化和基础架构搭建
- 🔐 用户认证系统（登录/登出）
- 👤 会员管理模块
  - 会员列表查看和搜索
  - 会员创建和基本信息管理
  - 健康标签系统
- 🔔 工作提醒系统
  - 多类型提醒支持（处方单、回访、订单、问卷、咨询、异常）
  - 优先级管理（紧急、重要、普通、信息）
  - 提醒列表和筛选功能
  - 批量操作和状态管理
- 📋 问卷管理模块
  - 问卷列表和状态管理
  - 问卷类型分类
  - 推送统计和完成率分析
- 📊 统计分析模块
  - 核心指标概览
  - 工作量统计表格
  - 健康指标统计
- 🎨 UI/UX设计
  - 医疗健康主题设计
  - 响应式布局支持
  - 现代化界面风格
  - 统一的组件规范

### 技术实现
- ⚡ Vue 3 + TypeScript + Vite 构建
- 🎯 Element Plus UI组件库
- 🗂️ Pinia 状态管理
- 🛣️ Vue Router 路由管理
- 📡 Axios HTTP客户端
- 🎨 CSS变量主题系统
- 📱 响应式栅格布局

### 开发工具
- 📝 ESLint + Prettier 代码规范
- 🔧 TypeScript 严格模式
- 📦 模块化项目结构
- 🌍 环境变量配置
- 📋 Git提交规范

### 文档
- 📖 完整的README文档
- 🏗️ 项目结构说明
- 🚀 快速开始指南
- 🎨 设计规范文档
- 📋 开发规范说明

## [0.1.0] - 2024-01-20

### 新增
- 项目初始化
- 基础目录结构搭建
- 开发环境配置

---

## 版本说明

- **主版本号**：当你做了不兼容的 API 修改
- **次版本号**：当你做了向下兼容的功能性新增
- **修订号**：当你做了向下兼容的问题修正

## 贡献指南

如果你想为这个项目做出贡献，请：

1. Fork 这个仓库
2. 创建你的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交你的修改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 问题反馈

如果你发现了 bug 或有功能建议，请在 [Issues](https://github.com/your-repo/issues) 页面提交。

## 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。
