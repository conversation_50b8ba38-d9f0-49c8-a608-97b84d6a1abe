<template>
  <el-dialog
    v-model="visible"
    title="新建处方单"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="会员" prop="memberId">
            <el-select
              v-model="form.memberId"
              placeholder="请选择会员"
              filterable
              remote
              :remote-method="searchMembers"
              :loading="memberLoading"
              style="width: 100%"
              @change="handleMemberChange"
            >
              <el-option
                v-for="member in memberOptions"
                :key="member.id"
                :label="member.name"
                :value="member.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级">
              <el-option label="普通" :value="1" />
              <el-option label="一般" :value="2" />
              <el-option label="重要" :value="3" />
              <el-option label="紧急" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="医生姓名" prop="doctorName">
            <el-input v-model="form.doctorName" placeholder="请输入医生姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="医院名称" prop="hospitalName">
            <el-input v-model="form.hospitalName" placeholder="请输入医院名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="科室" prop="department">
            <el-input v-model="form.department" placeholder="请输入科室" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="过期时间" prop="expiresAt">
            <el-date-picker
              v-model="form.expiresAt"
              type="datetime"
              placeholder="请选择过期时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="诊断结果" prop="diagnosis">
        <el-input
          v-model="form.diagnosis"
          type="textarea"
          :rows="3"
          placeholder="请输入诊断结果"
        />
      </el-form-item>

      <!-- 药品信息 -->
      <el-form-item label="药品信息">
        <div class="medication-list">
          <div class="medication-header">
            <span>药品列表</span>
            <el-button type="primary" size="small" @click="addMedication">
              添加药品
            </el-button>
          </div>
          
          <div v-if="form.medications.length === 0" class="empty-medications">
            <el-empty description="暂无药品信息" :image-size="80" />
          </div>
          
          <div v-else class="medication-items">
            <div
              v-for="(medication, index) in form.medications"
              :key="index"
              class="medication-item"
            >
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-input
                    v-model="medication.name"
                    placeholder="药品名称"
                    size="small"
                  />
                </el-col>
                <el-col :span="4">
                  <el-input
                    v-model="medication.specification"
                    placeholder="规格"
                    size="small"
                  />
                </el-col>
                <el-col :span="4">
                  <el-input
                    v-model="medication.dosage"
                    placeholder="用法用量"
                    size="small"
                  />
                </el-col>
                <el-col :span="3">
                  <el-input
                    v-model="medication.frequency"
                    placeholder="频率"
                    size="small"
                  />
                </el-col>
                <el-col :span="3">
                  <el-input
                    v-model="medication.duration"
                    placeholder="时长"
                    size="small"
                  />
                </el-col>
                <el-col :span="3">
                  <el-input
                    v-model="medication.notes"
                    placeholder="备注"
                    size="small"
                  />
                </el-col>
                <el-col :span="1">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeMedication(index)"
                  >
                    删除
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <!-- 附件上传 -->
      <el-form-item label="相关附件">
        <FileUpload
          :multiple="true"
          :limit="5"
          :max-size="10 * 1024 * 1024"
          :allowed-types="['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']"
          category="prescription"
          :related-type="'prescription'"
          tip-text="支持上传处方单相关文件"
          @success="handleFileSuccess"
          @remove="handleFileRemove"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { usePrescriptionStore } from '@/stores/prescription'
import { searchMembers as searchMembersApi } from '@/api/member'
import FileUpload from '@/components/FileUpload.vue'
import type { CreatePrescriptionParams, Medication } from '@/types/prescription'
import type { Member } from '@/types/member'
import type { FileInfo } from '@/types/file'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const prescriptionStore = usePrescriptionStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const memberLoading = ref(false)
const memberOptions = ref<Member[]>([])
const attachments = ref<string[]>([])

// 表单数据
const form = reactive<CreatePrescriptionParams>({
  memberId: 0,
  doctorId: 0,
  doctorName: '',
  hospitalName: '',
  department: '',
  diagnosis: '',
  medications: [],
  priority: 2,
  expiresAt: '',
  notes: '',
  attachments: []
})

// 表单验证规则
const rules: FormRules = {
  memberId: [
    { required: true, message: '请选择会员', trigger: 'change' }
  ],
  doctorName: [
    { required: true, message: '请输入医生姓名', trigger: 'blur' }
  ],
  hospitalName: [
    { required: true, message: '请输入医院名称', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请输入科室', trigger: 'blur' }
  ],
  diagnosis: [
    { required: true, message: '请输入诊断结果', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  expiresAt: [
    { required: true, message: '请选择过期时间', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 搜索会员
const searchMembers = async (query: string) => {
  if (!query) return
  
  try {
    memberLoading.value = true
    const response = await searchMembersApi({ keyword: query, limit: 20 })
    memberOptions.value = response.list
  } catch (error) {
    console.error('搜索会员失败:', error)
  } finally {
    memberLoading.value = false
  }
}

// 会员选择变化
const handleMemberChange = (memberId: number) => {
  const member = memberOptions.value.find(m => m.id === memberId)
  if (member) {
    // 可以根据会员信息自动填充一些字段
  }
}

// 添加药品
const addMedication = () => {
  const newMedication: Omit<Medication, 'id'> = {
    name: '',
    specification: '',
    dosage: '',
    frequency: '',
    duration: '',
    notes: ''
  }
  form.medications.push(newMedication)
}

// 删除药品
const removeMedication = (index: number) => {
  form.medications.splice(index, 1)
}

// 文件上传成功
const handleFileSuccess = (files: FileInfo[]) => {
  const newUrls = files.map(file => file.url)
  attachments.value.push(...newUrls)
  form.attachments = [...attachments.value]
}

// 文件删除
const handleFileRemove = (fileId: string) => {
  // 根据fileId从attachments中移除对应的URL
  // 这里需要根据实际的文件管理逻辑来实现
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    // 验证药品信息
    if (form.medications.length === 0) {
      ElMessage.warning('请至少添加一个药品')
      return
    }
    
    // 验证药品信息完整性
    const invalidMedication = form.medications.find(med => 
      !med.name || !med.specification || !med.dosage
    )
    if (invalidMedication) {
      ElMessage.warning('请完善药品信息（药品名称、规格、用法用量为必填项）')
      return
    }
    
    loading.value = true
    
    // 设置医生ID（这里可能需要根据医生姓名查询ID）
    form.doctorId = 1 // 临时设置，实际应该通过API查询
    
    await prescriptionStore.createPrescriptionAction(form)
    
    ElMessage.success('处方单创建成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('创建处方单失败:', error)
    ElMessage.error('创建处方单失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.assign(form, {
    memberId: 0,
    doctorId: 0,
    doctorName: '',
    hospitalName: '',
    department: '',
    diagnosis: '',
    medications: [],
    priority: 2,
    expiresAt: '',
    notes: '',
    attachments: []
  })
  
  attachments.value = []
  memberOptions.value = []
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    // 对话框打开时的初始化逻辑
  }
})
</script>

<style scoped>
.medication-list {
  width: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  padding: 16px;
}

.medication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
}

.empty-medications {
  text-align: center;
  padding: 20px 0;
}

.medication-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.medication-item {
  padding: 12px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  background: var(--el-bg-color-page);
}

.dialog-footer {
  text-align: right;
}
</style>
