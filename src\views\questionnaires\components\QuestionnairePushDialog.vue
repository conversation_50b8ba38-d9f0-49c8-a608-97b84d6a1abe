<template>
  <el-dialog
    v-model="visible"
    title="推送问卷"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-steps :active="currentStep" finish-status="success" align-center>
      <el-step title="选择问卷" />
      <el-step title="选择目标" />
      <el-step title="推送设置" />
      <el-step title="确认推送" />
    </el-steps>

    <div class="step-content">
      <!-- 步骤1: 选择问卷 -->
      <div v-if="currentStep === 0" class="step-panel">
        <h4>选择要推送的问卷</h4>
        <div class="questionnaire-search">
          <el-input
            v-model="questionnaireSearch"
            placeholder="搜索问卷名称"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="questionnaireType" placeholder="问卷类型" clearable style="margin-left: 10px">
            <el-option label="健康评估" value="health" />
            <el-option label="满意度调查" value="satisfaction" />
            <el-option label="生活习惯" value="lifestyle" />
            <el-option label="疾病筛查" value="screening" />
          </el-select>
        </div>
        
        <div class="questionnaire-list">
          <el-table
            :data="filteredQuestionnaires"
            @selection-change="handleQuestionnaireSelection"
            max-height="300"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" label="问卷标题" min-width="200" />
            <el-table-column prop="type" label="类型" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getQuestionnaireTypeText(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="questionCount" label="题目数" width="80" />
            <el-table-column prop="estimatedTime" label="预计时长" width="100">
              <template #default="{ row }">
                {{ row.estimatedTime }}分钟
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="previewQuestionnaire(row)">
                  预览
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 步骤2: 选择目标 -->
      <div v-if="currentStep === 1" class="step-panel">
        <h4>选择推送目标</h4>
        <el-radio-group v-model="targetType" @change="handleTargetTypeChange">
          <el-radio label="all">全部会员</el-radio>
          <el-radio label="group">按分组</el-radio>
          <el-radio label="tag">按标签</el-radio>
          <el-radio label="custom">自定义选择</el-radio>
        </el-radio-group>

        <!-- 按分组选择 -->
        <div v-if="targetType === 'group'" class="target-options">
          <el-select v-model="selectedGroups" multiple placeholder="请选择会员分组">
            <el-option
              v-for="group in memberGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </div>

        <!-- 按标签选择 -->
        <div v-if="targetType === 'tag'" class="target-options">
          <el-select v-model="selectedTags" multiple placeholder="请选择会员标签">
            <el-option
              v-for="tag in memberTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </div>

        <!-- 自定义选择 -->
        <div v-if="targetType === 'custom'" class="target-options">
          <div class="member-search">
            <el-input
              v-model="memberSearch"
              placeholder="搜索会员姓名或手机号"
              clearable
              style="width: 300px"
              @input="searchMembers"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <el-table
            :data="searchedMembers"
            @selection-change="handleMemberSelection"
            max-height="250"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="姓名" width="100" />
            <el-table-column prop="phone" label="手机号" width="120" />
            <el-table-column prop="age" label="年龄" width="80" />
            <el-table-column prop="gender" label="性别" width="80" />
            <el-table-column prop="tags" label="标签" min-width="150">
              <template #default="{ row }">
                <el-tag
                  v-for="tag in row.tags"
                  :key="tag"
                  size="small"
                  style="margin-right: 5px"
                >
                  {{ tag }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 目标统计 -->
        <div class="target-summary">
          <el-alert
            :title="`已选择 ${targetMemberCount} 名会员`"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <!-- 步骤3: 推送设置 -->
      <div v-if="currentStep === 2" class="step-panel">
        <h4>推送设置</h4>
        <el-form :model="pushSettings" label-width="120px">
          <el-form-item label="推送标题">
            <el-input
              v-model="pushSettings.title"
              placeholder="请输入推送标题"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="推送内容">
            <el-input
              v-model="pushSettings.content"
              type="textarea"
              :rows="3"
              placeholder="请输入推送内容"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="推送时间">
            <el-radio-group v-model="pushSettings.timeType">
              <el-radio label="now">立即推送</el-radio>
              <el-radio label="scheduled">定时推送</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item v-if="pushSettings.timeType === 'scheduled'" label="推送时间">
            <el-date-picker
              v-model="pushSettings.scheduledTime"
              type="datetime"
              placeholder="请选择推送时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          
          <el-form-item label="有效期">
            <el-select v-model="pushSettings.validDays" placeholder="请选择有效期">
              <el-option label="1天" :value="1" />
              <el-option label="3天" :value="3" />
              <el-option label="7天" :value="7" />
              <el-option label="15天" :value="15" />
              <el-option label="30天" :value="30" />
              <el-option label="永久有效" :value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="推送渠道">
            <el-checkbox-group v-model="pushSettings.channels">
              <el-checkbox label="wechat">微信公众号</el-checkbox>
              <el-checkbox label="sms">短信通知</el-checkbox>
              <el-checkbox label="app">APP推送</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="提醒设置">
            <el-checkbox v-model="pushSettings.enableReminder">启用提醒</el-checkbox>
            <div v-if="pushSettings.enableReminder" style="margin-top: 10px">
              <el-checkbox-group v-model="pushSettings.reminderDays">
                <el-checkbox :label="1">1天后提醒</el-checkbox>
                <el-checkbox :label="3">3天后提醒</el-checkbox>
                <el-checkbox :label="7">7天后提醒</el-checkbox>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤4: 确认推送 -->
      <div v-if="currentStep === 3" class="step-panel">
        <h4>确认推送信息</h4>
        <div class="push-summary">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="问卷数量">
              {{ selectedQuestionnaires.length }} 份
            </el-descriptions-item>
            <el-descriptions-item label="目标会员">
              {{ targetMemberCount }} 人
            </el-descriptions-item>
            <el-descriptions-item label="推送标题">
              {{ pushSettings.title }}
            </el-descriptions-item>
            <el-descriptions-item label="推送时间">
              {{ pushSettings.timeType === 'now' ? '立即推送' : pushSettings.scheduledTime }}
            </el-descriptions-item>
            <el-descriptions-item label="有效期">
              {{ pushSettings.validDays === 0 ? '永久有效' : `${pushSettings.validDays}天` }}
            </el-descriptions-item>
            <el-descriptions-item label="推送渠道">
              {{ pushSettings.channels.join(', ') }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="questionnaire-preview">
            <h5>问卷列表</h5>
            <el-table :data="selectedQuestionnaires" size="small">
              <el-table-column prop="title" label="问卷标题" />
              <el-table-column prop="type" label="类型" width="120">
                <template #default="{ row }">
                  <el-tag size="small">{{ getQuestionnaireTypeText(row.type) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="estimatedTime" label="预计时长" width="100">
                <template #default="{ row }">
                  {{ row.estimatedTime }}分钟
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="currentStep < 3"
          type="primary"
          :disabled="!canNextStep"
          @click="nextStep"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 3"
          type="primary"
          :loading="loading"
          @click="handlePush"
        >
          确认推送
        </el-button>
      </div>
    </template>

    <!-- 问卷预览对话框 -->
    <el-dialog v-model="showPreviewDialog" title="问卷预览" width="600px">
      <div v-if="previewQuestionnaire" class="questionnaire-preview-content">
        <h3>{{ previewQuestionnaire.title }}</h3>
        <p>{{ previewQuestionnaire.description }}</p>
        <div class="question-list">
          <div
            v-for="(question, index) in previewQuestionnaire.questions"
            :key="index"
            class="question-item"
          >
            <h4>{{ index + 1 }}. {{ question.title }}</h4>
            <div v-if="question.type === 'single'" class="options">
              <el-radio-group disabled>
                <el-radio
                  v-for="option in question.options"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </div>
            <div v-else-if="question.type === 'multiple'" class="options">
              <el-checkbox-group disabled>
                <el-checkbox
                  v-for="option in question.options"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div v-else-if="question.type === 'text'" class="options">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="文本输入"
                disabled
              />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useQuestionnaireStore } from '@/stores/questionnaire'
import { useMemberStore } from '@/stores/member'
import type { Questionnaire, QuestionnairePushParams } from '@/types/questionnaire'
import type { Member } from '@/types/member'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const questionnaireStore = useQuestionnaireStore()
const memberStore = useMemberStore()

// 响应式数据
const loading = ref(false)
const currentStep = ref(0)
const showPreviewDialog = ref(false)
const previewQuestionnaireData = ref<Questionnaire | null>(null)

// 问卷选择
const questionnaireSearch = ref('')
const questionnaireType = ref('')
const selectedQuestionnaires = ref<Questionnaire[]>([])
const availableQuestionnaires = ref<Questionnaire[]>([])

// 目标选择
const targetType = ref('all')
const selectedGroups = ref<number[]>([])
const selectedTags = ref<string[]>([])
const memberSearch = ref('')
const searchedMembers = ref<Member[]>([])
const selectedMembers = ref<Member[]>([])
const memberGroups = ref([
  { id: 1, name: '高血压患者' },
  { id: 2, name: '糖尿病患者' },
  { id: 3, name: '老年人群' },
  { id: 4, name: 'VIP会员' }
])
const memberTags = ref(['高血压', '糖尿病', '高血脂', '心脏病', '老年人'])

// 推送设置
const pushSettings = reactive({
  title: '',
  content: '',
  timeType: 'now',
  scheduledTime: '',
  validDays: 7,
  channels: ['wechat'],
  enableReminder: true,
  reminderDays: [3, 7]
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filteredQuestionnaires = computed(() => {
  return availableQuestionnaires.value.filter(q => {
    const matchSearch = !questionnaireSearch.value || 
      q.title.toLowerCase().includes(questionnaireSearch.value.toLowerCase())
    const matchType = !questionnaireType.value || q.type === questionnaireType.value
    return matchSearch && matchType
  })
})

const targetMemberCount = computed(() => {
  switch (targetType.value) {
    case 'all':
      return 1000 // 模拟总会员数
    case 'group':
      return selectedGroups.value.length * 50 // 模拟每组50人
    case 'tag':
      return selectedTags.value.length * 30 // 模拟每标签30人
    case 'custom':
      return selectedMembers.value.length
    default:
      return 0
  }
})

const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedQuestionnaires.value.length > 0
    case 1:
      return targetMemberCount.value > 0
    case 2:
      return pushSettings.title && pushSettings.content
    default:
      return true
  }
})

// 方法
const getQuestionnaireTypeText = (type: string) => {
  const types = {
    health: '健康评估',
    satisfaction: '满意度调查',
    lifestyle: '生活习惯',
    screening: '疾病筛查'
  }
  return types[type] || type
}

const handleQuestionnaireSelection = (selection: Questionnaire[]) => {
  selectedQuestionnaires.value = selection
}

const previewQuestionnaire = (questionnaire: Questionnaire) => {
  previewQuestionnaireData.value = questionnaire
  showPreviewDialog.value = true
}

const handleTargetTypeChange = () => {
  selectedGroups.value = []
  selectedTags.value = []
  selectedMembers.value = []
}

const searchMembers = async () => {
  if (!memberSearch.value) {
    searchedMembers.value = []
    return
  }
  
  try {
    const response = await memberStore.searchMembers({
      keyword: memberSearch.value,
      limit: 50
    })
    searchedMembers.value = response.list
  } catch (error) {
    console.error('搜索会员失败:', error)
  }
}

const handleMemberSelection = (selection: Member[]) => {
  selectedMembers.value = selection
}

const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handlePush = async () => {
  try {
    loading.value = true
    
    const pushParams: QuestionnairePushParams = {
      questionnaireIds: selectedQuestionnaires.value.map(q => q.id),
      targetType: targetType.value,
      targetGroups: selectedGroups.value,
      targetTags: selectedTags.value,
      targetMembers: selectedMembers.value.map(m => m.id),
      title: pushSettings.title,
      content: pushSettings.content,
      scheduledTime: pushSettings.timeType === 'scheduled' ? pushSettings.scheduledTime : undefined,
      validDays: pushSettings.validDays,
      channels: pushSettings.channels,
      enableReminder: pushSettings.enableReminder,
      reminderDays: pushSettings.reminderDays
    }
    
    await questionnaireStore.pushQuestionnaireAction(pushParams)
    
    ElMessage.success('问卷推送成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('推送问卷失败:', error)
    ElMessage.error('推送问卷失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  currentStep.value = 0
  selectedQuestionnaires.value = []
  targetType.value = 'all'
  selectedGroups.value = []
  selectedTags.value = []
  selectedMembers.value = []
  searchedMembers.value = []
  memberSearch.value = ''
  questionnaireSearch.value = ''
  questionnaireType.value = ''
  
  Object.assign(pushSettings, {
    title: '',
    content: '',
    timeType: 'now',
    scheduledTime: '',
    validDays: 7,
    channels: ['wechat'],
    enableReminder: true,
    reminderDays: [3, 7]
  })
}

// 监听对话框打开
watch(visible, async (newVal) => {
  if (newVal) {
    try {
      availableQuestionnaires.value = await questionnaireStore.fetchQuestionnaireLibrary()
    } catch (error) {
      console.error('获取问卷库失败:', error)
    }
  }
})
</script>

<style scoped>
.step-content {
  margin: 30px 0;
  min-height: 400px;
}

.step-panel h4 {
  margin: 0 0 20px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.questionnaire-search {
  margin-bottom: 20px;
}

.questionnaire-list {
  margin-top: 20px;
}

.target-options {
  margin-top: 20px;
}

.member-search {
  margin-bottom: 15px;
}

.target-summary {
  margin-top: 20px;
}

.push-summary {
  background: var(--el-bg-color-page);
  padding: 20px;
  border-radius: 6px;
}

.questionnaire-preview {
  margin-top: 20px;
}

.questionnaire-preview h5 {
  margin: 0 0 10px 0;
  color: var(--el-text-color-primary);
}

.questionnaire-preview-content h3 {
  margin: 0 0 10px 0;
  color: var(--el-text-color-primary);
}

.question-list {
  margin-top: 20px;
}

.question-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
}

.question-item h4 {
  margin: 0 0 10px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.options {
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-steps) {
  margin-bottom: 20px;
}
</style>
