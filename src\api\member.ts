import request from './request'
import type { 
  Member, 
  MemberForm, 
  MemberSearchParams, 
  HealthIndicator, 
  VisitRecord,
  ApiResponse, 
  PaginationResponse 
} from '@/types'

// 获取会员列表
export const getMemberList = (params: MemberSearchParams): Promise<ApiResponse<PaginationResponse<Member>>> => {
  return request.get('/members', { params })
}

// 获取会员详情
export const getMemberDetail = (id: number): Promise<ApiResponse<Member>> => {
  return request.get(`/members/${id}`)
}

// 创建会员
export const createMember = (data: MemberForm): Promise<ApiResponse<Member>> => {
  return request.post('/members', data)
}

// 更新会员信息
export const updateMember = (id: number, data: Partial<MemberForm>): Promise<ApiResponse<Member>> => {
  return request.put(`/members/${id}`, data)
}

// 删除会员
export const deleteMember = (id: number): Promise<ApiResponse> => {
  return request.delete(`/members/${id}`)
}

// 上传会员媒体文件
export const uploadMemberMedia = (memberId: number, files: File[]): Promise<ApiResponse<string[]>> => {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  
  return request.post(`/members/${memberId}/media`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取会员健康指标
export const getMemberHealthIndicators = (memberId: number, params?: {
  type?: string
  dateFrom?: string
  dateTo?: string
}): Promise<ApiResponse<HealthIndicator[]>> => {
  return request.get(`/members/${memberId}/health-indicators`, { params })
}

// 添加健康指标
export const addHealthIndicator = (memberId: number, data: {
  indicatorType: string
  value: string
  unit: string
  measuredAt: string
  notes?: string
}): Promise<ApiResponse<HealthIndicator>> => {
  return request.post(`/members/${memberId}/health-indicators`, data)
}

// 获取会员回访记录
export const getMemberVisits = (memberId: number): Promise<ApiResponse<VisitRecord[]>> => {
  return request.get(`/members/${memberId}/visits`)
}

// 创建回访记录
export const createVisitRecord = (data: {
  memberId: number
  visitType: number
  scheduledAt: string
  location: {
    latitude: number
    longitude: number
    address: string
  }
  description: string
  photos?: string[]
}): Promise<ApiResponse<VisitRecord>> => {
  return request.post('/visits', data)
}

// 完成回访
export const completeVisit = (visitId: number, data: {
  description: string
  photos?: string[]
  satisfactionScore?: number
}): Promise<ApiResponse<VisitRecord>> => {
  return request.put(`/visits/${visitId}/complete`, data)
}

// 获取会员健康数据汇总
export const getMemberHealthSummary = (memberId: number): Promise<ApiResponse<{
  basicInfo: any
  healthIndicators: any
  medicalHistory: any
  lifestyleData: any
  recentReports: any[]
}>> => {
  return request.get(`/members/${memberId}/health-summary`)
}
