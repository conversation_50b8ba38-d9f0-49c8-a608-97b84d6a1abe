<template>
  <el-dialog
    v-model="visible"
    title="录入健康指标"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="会员" prop="memberId">
            <el-select
              v-model="form.memberId"
              placeholder="请选择会员"
              filterable
              remote
              :remote-method="searchMembers"
              :loading="memberLoading"
              style="width: 100%"
              @change="handleMemberChange"
            >
              <el-option
                v-for="member in memberOptions"
                :key="member.id"
                :label="member.name"
                :value="member.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指标类型" prop="indicatorType">
            <el-select 
              v-model="form.indicatorType" 
              placeholder="请选择指标类型"
              @change="handleIndicatorTypeChange"
            >
              <el-option label="血压" value="blood_pressure" />
              <el-option label="血糖" value="blood_sugar" />
              <el-option label="血脂" value="blood_lipid" />
              <el-option label="心率" value="heart_rate" />
              <el-option label="体重" value="weight" />
              <el-option label="BMI" value="bmi" />
              <el-option label="体温" value="temperature" />
              <el-option label="血氧饱和度" value="oxygen_saturation" />
              <el-option label="胆固醇" value="cholesterol" />
              <el-option label="尿酸" value="uric_acid" />
              <el-option label="肌酐" value="creatinine" />
              <el-option label="血红蛋白" value="hemoglobin" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 血压特殊处理 -->
      <el-row v-if="form.indicatorType === 'blood_pressure'" :gutter="20">
        <el-col :span="12">
          <el-form-item label="收缩压" prop="systolic">
            <el-input-number
              v-model="systolic"
              :min="50"
              :max="300"
              :precision="0"
              placeholder="收缩压"
              style="width: 100%"
              @change="updateBloodPressureValue"
            />
            <span class="unit-text">mmHg</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="舒张压" prop="diastolic">
            <el-input-number
              v-model="diastolic"
              :min="30"
              :max="200"
              :precision="0"
              placeholder="舒张压"
              style="width: 100%"
              @change="updateBloodPressureValue"
            />
            <span class="unit-text">mmHg</span>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他指标 -->
      <el-form-item v-else label="指标值" prop="value">
        <el-input-number
          v-model="form.value"
          :min="0"
          :max="currentConfig?.maxValue || 1000"
          :precision="currentConfig?.decimalPlaces || 1"
          :placeholder="`请输入${getIndicatorName(form.indicatorType)}值`"
          style="width: 200px"
          @change="handleValueChange"
        />
        <span class="unit-text">{{ currentConfig?.unit || '' }}</span>
        <span v-if="currentConfig?.normalRange" class="reference-range">
          参考范围: {{ currentConfig.normalRange.min }} - {{ currentConfig.normalRange.max }}
        </span>
      </el-form-item>

      <el-form-item label="测量时间" prop="measuredAt">
        <el-date-picker
          v-model="form.measuredAt"
          type="datetime"
          placeholder="请选择测量时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="测量设备">
        <el-input
          v-model="form.deviceInfo"
          placeholder="请输入测量设备信息（可选）"
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
        />
      </el-form-item>

      <!-- 异常提示 -->
      <el-alert
        v-if="abnormalInfo"
        :title="abnormalInfo.message"
        :type="getAlertType(abnormalInfo.abnormalLevel)"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      >
        <template #default>
          <div v-if="abnormalInfo.suggestions.length > 0">
            <p><strong>建议：</strong></p>
            <ul>
              <li v-for="suggestion in abnormalInfo.suggestions" :key="suggestion">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </template>
      </el-alert>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useHealthStore } from '@/stores/health'
import { searchMembers as searchMembersApi } from '@/api/member'
import type { 
  CreateHealthRecordParams, 
  HealthIndicatorType, 
  HealthIndicatorConfig,
  AbnormalLevel 
} from '@/types/health'
import type { Member } from '@/types/member'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const healthStore = useHealthStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const memberLoading = ref(false)
const memberOptions = ref<Member[]>([])
const systolic = ref<number>()
const diastolic = ref<number>()
const abnormalInfo = ref<any>(null)

// 表单数据
const form = reactive<CreateHealthRecordParams>({
  memberId: 0,
  indicatorType: '' as HealthIndicatorType,
  value: '',
  measuredAt: '',
  notes: '',
  deviceInfo: ''
})

// 表单验证规则
const rules: FormRules = {
  memberId: [
    { required: true, message: '请选择会员', trigger: 'change' }
  ],
  indicatorType: [
    { required: true, message: '请选择指标类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入指标值', trigger: 'blur' }
  ],
  systolic: [
    { required: true, message: '请输入收缩压', trigger: 'blur' }
  ],
  diastolic: [
    { required: true, message: '请输入舒张压', trigger: 'blur' }
  ],
  measuredAt: [
    { required: true, message: '请选择测量时间', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentConfig = computed(() => {
  return healthStore.getIndicatorConfig(form.indicatorType)
})

// 搜索会员
const searchMembers = async (query: string) => {
  if (!query) return
  
  try {
    memberLoading.value = true
    const response = await searchMembersApi({ keyword: query, limit: 20 })
    memberOptions.value = response.list
  } catch (error) {
    console.error('搜索会员失败:', error)
  } finally {
    memberLoading.value = false
  }
}

// 会员选择变化
const handleMemberChange = (memberId: number) => {
  const member = memberOptions.value.find(m => m.id === memberId)
  if (member) {
    // 可以根据会员信息预填充一些内容
  }
}

// 指标类型变化
const handleIndicatorTypeChange = () => {
  // 重置值
  form.value = ''
  systolic.value = undefined
  diastolic.value = undefined
  abnormalInfo.value = null
  
  // 设置默认测量时间为当前时间
  if (!form.measuredAt) {
    form.measuredAt = new Date().toISOString().slice(0, 19).replace('T', ' ')
  }
}

// 更新血压值
const updateBloodPressureValue = () => {
  if (systolic.value && diastolic.value) {
    form.value = `${systolic.value}/${diastolic.value}`
    checkAbnormal()
  }
}

// 指标值变化
const handleValueChange = () => {
  checkAbnormal()
}

// 检查异常
const checkAbnormal = async () => {
  if (!form.indicatorType || !form.value) return
  
  try {
    const result = await healthStore.checkHealthAbnormalAction(form.indicatorType, form.value)
    abnormalInfo.value = result
  } catch (error) {
    console.error('检查异常失败:', error)
  }
}

// 获取指标名称
const getIndicatorName = (type: HealthIndicatorType) => {
  const names = {
    blood_pressure: '血压',
    blood_sugar: '血糖',
    blood_lipid: '血脂',
    heart_rate: '心率',
    weight: '体重',
    bmi: 'BMI',
    temperature: '体温',
    oxygen_saturation: '血氧饱和度',
    cholesterol: '胆固醇',
    uric_acid: '尿酸',
    creatinine: '肌酐',
    hemoglobin: '血红蛋白'
  }
  return names[type] || type
}

// 获取警告类型
const getAlertType = (level: AbnormalLevel) => {
  const types = ['success', 'warning', 'warning', 'error', 'error']
  return types[level] || 'info'
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    await healthStore.createHealthRecordAction(form)
    
    ElMessage.success('健康指标录入成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('录入健康指标失败:', error)
    ElMessage.error('录入健康指标失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.assign(form, {
    memberId: 0,
    indicatorType: '' as HealthIndicatorType,
    value: '',
    measuredAt: '',
    notes: '',
    deviceInfo: ''
  })
  
  systolic.value = undefined
  diastolic.value = undefined
  abnormalInfo.value = null
  memberOptions.value = []
}

// 监听对话框打开
watch(visible, async (newVal) => {
  if (newVal) {
    // 获取指标配置
    await healthStore.fetchIndicatorConfigs()
  }
})
</script>

<style scoped>
.unit-text {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.reference-range {
  margin-left: 16px;
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>
