/**
 * 健康指标相关类型定义
 */

// 健康指标类型
export enum HealthIndicatorType {
  BLOOD_PRESSURE = 'blood_pressure',      // 血压
  BLOOD_SUGAR = 'blood_sugar',            // 血糖
  BLOOD_LIPID = 'blood_lipid',            // 血脂
  HEART_RATE = 'heart_rate',              // 心率
  WEIGHT = 'weight',                      // 体重
  BMI = 'bmi',                           // BMI
  TEMPERATURE = 'temperature',            // 体温
  OXYGEN_SATURATION = 'oxygen_saturation', // 血氧饱和度
  CHOLESTEROL = 'cholesterol',            // 胆固醇
  URIC_ACID = 'uric_acid',               // 尿酸
  CREATININE = 'creatinine',             // 肌酐
  HEMOGLOBIN = 'hemoglobin'              // 血红蛋白
}

// 异常等级
export enum AbnormalLevel {
  NORMAL = 0,           // 正常
  MILD = 1,            // 轻度异常
  MODERATE = 2,        // 中度异常
  SEVERE = 3,          // 重度异常
  CRITICAL = 4         // 危急
}

// 健康指标记录
export interface HealthRecord {
  id: number
  memberId: number                      // 会员ID
  memberName: string                    // 会员姓名
  indicatorType: HealthIndicatorType    // 指标类型
  value: number | string               // 指标值
  unit: string                         // 单位
  referenceRange: string               // 参考范围
  abnormalLevel: AbnormalLevel         // 异常等级
  isAbnormal: boolean                  // 是否异常
  notes?: string                       // 备注
  measuredAt: string                   // 测量时间
  recordedBy: string                   // 记录人
  deviceInfo?: string                  // 测量设备信息
  createdAt: string
  updatedAt: string
}

// 血压记录（特殊处理）
export interface BloodPressureRecord extends Omit<HealthRecord, 'value'> {
  systolic: number                     // 收缩压
  diastolic: number                    // 舒张压
  value: string                        // 格式化值 "120/80"
}

// 健康指标配置
export interface HealthIndicatorConfig {
  type: HealthIndicatorType
  name: string                         // 指标名称
  unit: string                         // 单位
  normalRange: {                       // 正常范围
    min: number
    max: number
  }
  abnormalRanges: {                    // 异常范围配置
    mild: { min?: number; max?: number }
    moderate: { min?: number; max?: number }
    severe: { min?: number; max?: number }
    critical: { min?: number; max?: number }
  }
  isHigherBetter: boolean              // 数值越高越好
  decimalPlaces: number                // 小数位数
  description: string                  // 描述
}

// 健康指标查询参数
export interface HealthQuery {
  page?: number
  limit?: number
  memberId?: number
  memberName?: string
  indicatorType?: HealthIndicatorType
  abnormalLevel?: AbnormalLevel
  isAbnormal?: boolean
  startDate?: string
  endDate?: string
  keyword?: string
}

// 健康指标统计
export interface HealthStats {
  totalRecords: number                 // 总记录数
  normalCount: number                  // 正常记录数
  abnormalCount: number                // 异常记录数
  abnormalRate: number                 // 异常率
  todayRecords: number                 // 今日记录数
  weeklyRecords: number                // 本周记录数
  monthlyRecords: number               // 本月记录数
  
  // 按异常等级统计
  mildAbnormal: number                 // 轻度异常
  moderateAbnormal: number             // 中度异常
  severeAbnormal: number               // 重度异常
  criticalAbnormal: number             // 危急异常
  
  // 按指标类型统计
  indicatorStats: {
    type: HealthIndicatorType
    count: number
    abnormalCount: number
    abnormalRate: number
  }[]
}

// 健康趋势数据
export interface HealthTrend {
  memberId: number
  memberName: string
  indicatorType: HealthIndicatorType
  data: {
    date: string
    value: number | string
    abnormalLevel: AbnormalLevel
  }[]
  trend: 'up' | 'down' | 'stable'      // 趋势方向
  changeRate: number                   // 变化率
  lastValue: number | string           // 最新值
  avgValue: number                     // 平均值
}

// 异常预警
export interface HealthAlert {
  id: number
  memberId: number                     // 会员ID
  memberName: string                   // 会员姓名
  indicatorType: HealthIndicatorType   // 指标类型
  value: number | string              // 异常值
  abnormalLevel: AbnormalLevel        // 异常等级
  alertMessage: string                // 预警消息
  suggestions: string[]               // 处理建议
  isProcessed: boolean                // 是否已处理
  processedBy?: string                // 处理人
  processedAt?: string                // 处理时间
  createdAt: string
}

// 创建健康记录参数
export interface CreateHealthRecordParams {
  memberId: number
  indicatorType: HealthIndicatorType
  value: number | string
  measuredAt: string
  notes?: string
  deviceInfo?: string
}

// 批量导入参数
export interface BatchImportParams {
  file: File
  memberId?: number                    // 指定会员ID（可选）
  skipErrors: boolean                  // 是否跳过错误行
}

// 导入结果
export interface ImportResult {
  total: number                        // 总行数
  success: number                      // 成功导入数
  failed: number                       // 失败数
  errors: {                           // 错误详情
    row: number
    error: string
  }[]
  records: HealthRecord[]             // 成功导入的记录
}

// 健康报告
export interface HealthReport {
  memberId: number
  memberName: string
  reportDate: string
  period: string                       // 报告周期
  summary: {
    totalIndicators: number            // 总指标数
    normalCount: number                // 正常数量
    abnormalCount: number              // 异常数量
    improvementCount: number           // 改善数量
    deteriorationCount: number         // 恶化数量
  }
  indicators: {
    type: HealthIndicatorType
    name: string
    latestValue: number | string
    trend: 'up' | 'down' | 'stable'
    abnormalLevel: AbnormalLevel
    suggestions: string[]
  }[]
  overallAssessment: string            // 整体评估
  recommendations: string[]            // 建议
}

// 健康目标
export interface HealthGoal {
  id: number
  memberId: number
  memberName: string
  indicatorType: HealthIndicatorType
  targetValue: number
  currentValue: number
  targetDate: string
  progress: number                     // 进度百分比
  status: 'active' | 'achieved' | 'overdue' | 'cancelled'
  description: string
  createdAt: string
  updatedAt: string
}
