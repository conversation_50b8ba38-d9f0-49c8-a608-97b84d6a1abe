import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import { login, logout, getUserInfo } from '@/api/auth'
import type { LoginForm, UserInfo } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(Cookies.get('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])

  const isLoggedIn = computed(() => !!token.value)

  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm)
      token.value = response.data.token
      userInfo.value = response.data.userInfo
      
      // 保存token到cookie
      Cookies.set('token', token.value, { expires: 7 })
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = ''
      userInfo.value = null
      Cookies.remove('token')
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      userInfo.value = response.data
      // 模拟权限数据，实际应从API获取
      permissions.value = ['dashboard:view', 'member:manage', 'reminder:manage', 'questionnaire:manage', 'statistics:view']
      return response
    } catch (error) {
      throw error
    }
  }

  // 初始化用户信息
  const initUser = async () => {
    if (token.value && !userInfo.value) {
      try {
        await getUserInfoAction()
      } catch (error) {
        // token无效，清除登录状态
        logoutAction()
      }
    }
  }

  return {
    token,
    userInfo,
    permissions,
    isLoggedIn,
    loginAction,
    logoutAction,
    getUserInfoAction,
    initUser
  }
})
