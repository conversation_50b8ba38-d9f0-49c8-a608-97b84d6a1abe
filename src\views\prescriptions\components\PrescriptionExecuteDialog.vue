<template>
  <el-dialog
    v-model="visible"
    title="执行处方单"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="prescription" class="prescription-info">
      <el-descriptions title="处方单信息" :column="2" border>
        <el-descriptions-item label="处方单号">
          {{ prescription.prescriptionNo }}
        </el-descriptions-item>
        <el-descriptions-item label="会员姓名">
          {{ prescription.memberName }}
        </el-descriptions-item>
        <el-descriptions-item label="医生姓名">
          {{ prescription.doctorName }}
        </el-descriptions-item>
        <el-descriptions-item label="医院科室">
          {{ prescription.hospitalName }} - {{ prescription.department }}
        </el-descriptions-item>
        <el-descriptions-item label="诊断结果" :span="2">
          {{ prescription.diagnosis }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 药品信息 -->
      <div class="medication-section">
        <h4>药品信息</h4>
        <el-table :data="prescription.medications" border size="small">
          <el-table-column prop="name" label="药品名称" width="150" />
          <el-table-column prop="specification" label="规格" width="100" />
          <el-table-column prop="dosage" label="用法用量" width="120" />
          <el-table-column prop="frequency" label="服用频率" width="100" />
          <el-table-column prop="duration" label="服用时长" width="100" />
          <el-table-column prop="notes" label="备注" show-overflow-tooltip />
        </el-table>
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      style="margin-top: 20px"
    >
      <el-form-item label="执行计划" prop="executionPlan">
        <el-input
          v-model="form.executionPlan"
          type="textarea"
          :rows="3"
          placeholder="请详细描述执行计划，包括药品配送、服用指导等"
        />
      </el-form-item>

      <el-form-item label="执行说明" prop="executionNotes">
        <el-input
          v-model="form.executionNotes"
          type="textarea"
          :rows="3"
          placeholder="请描述具体的执行过程和注意事项"
        />
      </el-form-item>

      <el-form-item label="执行结果" prop="executionResult">
        <el-input
          v-model="form.executionResult"
          type="textarea"
          :rows="3"
          placeholder="请描述执行结果，包括会员反应、药品使用情况等"
        />
      </el-form-item>

      <el-form-item label="会员反馈" prop="memberFeedback">
        <el-input
          v-model="form.memberFeedback"
          type="textarea"
          :rows="2"
          placeholder="记录会员的反馈意见"
        />
      </el-form-item>

      <el-form-item label="满意度评分" prop="satisfactionScore">
        <el-rate
          v-model="form.satisfactionScore"
          :max="5"
          show-text
          :texts="['很差', '较差', '一般', '满意', '很满意']"
        />
      </el-form-item>

      <el-form-item label="执行图片">
        <FileUpload
          :multiple="true"
          :limit="10"
          :max-size="5 * 1024 * 1024"
          :allowed-types="['jpg', 'jpeg', 'png']"
          category="prescription_execution"
          :related-type="'prescription'"
          :related-id="prescriptionId"
          tip-text="上传执行过程的相关图片"
          @success="handleImageSuccess"
          @remove="handleImageRemove"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          完成执行
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { usePrescriptionStore } from '@/stores/prescription'
import FileUpload from '@/components/FileUpload.vue'
import type { ExecutePrescriptionParams } from '@/types/prescription'
import type { FileInfo } from '@/types/file'

interface Props {
  modelValue: boolean
  prescriptionId?: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const prescriptionStore = usePrescriptionStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const executionImages = ref<string[]>([])

// 表单数据
const form = reactive<ExecutePrescriptionParams>({
  executionPlan: '',
  executionNotes: '',
  executionResult: '',
  executionImages: [],
  memberFeedback: '',
  satisfactionScore: 5
})

// 表单验证规则
const rules: FormRules = {
  executionPlan: [
    { required: true, message: '请输入执行计划', trigger: 'blur' },
    { min: 10, message: '执行计划至少需要10个字符', trigger: 'blur' }
  ],
  executionNotes: [
    { required: true, message: '请输入执行说明', trigger: 'blur' },
    { min: 10, message: '执行说明至少需要10个字符', trigger: 'blur' }
  ],
  executionResult: [
    { required: true, message: '请输入执行结果', trigger: 'blur' },
    { min: 10, message: '执行结果至少需要10个字符', trigger: 'blur' }
  ],
  satisfactionScore: [
    { required: true, message: '请选择满意度评分', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const prescription = computed(() => prescriptionStore.currentPrescription)

// 图片上传成功
const handleImageSuccess = (files: FileInfo[]) => {
  const newUrls = files.map(file => file.url)
  executionImages.value.push(...newUrls)
  form.executionImages = [...executionImages.value]
}

// 图片删除
const handleImageRemove = (fileId: string) => {
  // 根据fileId从executionImages中移除对应的URL
  // 这里需要根据实际的文件管理逻辑来实现
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.prescriptionId) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    await prescriptionStore.executePrescriptionAction(props.prescriptionId, form)
    
    ElMessage.success('处方单执行完成')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('执行处方单失败:', error)
    ElMessage.error('执行处方单失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.assign(form, {
    executionPlan: '',
    executionNotes: '',
    executionResult: '',
    executionImages: [],
    memberFeedback: '',
    satisfactionScore: 5
  })
  
  executionImages.value = []
}

// 监听对话框打开
watch(visible, async (newVal) => {
  if (newVal && props.prescriptionId) {
    // 获取处方单详情
    try {
      await prescriptionStore.fetchPrescriptionDetail(props.prescriptionId)
    } catch (error) {
      console.error('获取处方单详情失败:', error)
      ElMessage.error('获取处方单详情失败')
      handleClose()
    }
  }
})

// 监听处方单ID变化
watch(() => props.prescriptionId, async (newId) => {
  if (newId && visible.value) {
    try {
      await prescriptionStore.fetchPrescriptionDetail(newId)
    } catch (error) {
      console.error('获取处方单详情失败:', error)
    }
  }
})
</script>

<style scoped>
.prescription-info {
  margin-bottom: 20px;
}

.medication-section {
  margin-top: 20px;
}

.medication-section h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__title) {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.el-rate__text) {
  color: var(--el-text-color-regular);
}
</style>
