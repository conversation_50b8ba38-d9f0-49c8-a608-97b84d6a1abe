import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },

  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layout/Layout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/views/Dashboard.vue')
      },
      {
        path: '/members',
        name: 'Members',
        component: () => import('@/views/members/MemberList.vue')
      },
      {
        path: '/members/create',
        name: 'MemberCreate',
        component: () => import('@/views/members/MemberCreate.vue')
      },

      {
        path: '/questionnaires',
        name: 'Questionnaires',
        component: () => import('@/views/questionnaires/QuestionnaireList.vue')
      },

      {
        path: '/statistics',
        name: 'Statistics',
        component: () => import('@/views/statistics/Statistics.vue')
      },

      {
        path: '/reminders',
        name: 'Reminders',
        component: () => import('@/views/reminders/ReminderList.vue')
      },

      // 处方单管理
      {
        path: '/prescriptions',
        name: 'PrescriptionList',
        component: () => import('@/views/prescriptions/PrescriptionList.vue')
      },
      {
        path: '/prescriptions/:id',
        name: 'PrescriptionDetail',
        component: () => import('@/views/prescriptions/PrescriptionDetail.vue')
      },

      // 回访管理
      {
        path: '/visits',
        name: 'VisitList',
        component: () => import('@/views/visits/VisitList.vue')
      },
      {
        path: '/visits/:id',
        name: 'VisitDetail',
        component: () => import('@/views/visits/VisitDetail.vue')
      },

      // 健康指标管理
      {
        path: '/health',
        name: 'HealthIndicators',
        component: () => import('@/views/health/HealthIndicators.vue')
      },
      {
        path: '/health/charts/:memberId',
        name: 'HealthCharts',
        component: () => import('@/views/health/HealthCharts.vue')
      },

      // 文件管理
      {
        path: '/files',
        name: 'FileManagement',
        component: () => import('@/views/files/FileManagement.vue')
      },

    ]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to: RouteLocationNormalized, _from: RouteLocationNormalized, next: NavigationGuardNext) => {
  const userStore = useUserStore()

  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else if (to.path === '/login' && userStore.isLoggedIn) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router
