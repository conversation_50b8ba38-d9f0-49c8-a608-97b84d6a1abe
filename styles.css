* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    width: 85%;
    max-width: 1200px;
    margin: 0 auto;
}

header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem 0;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
}

.hero {
    background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('medical-bg.jpg');
    background-size: cover;
    color: white;
    text-align: center;
    padding: 5rem 0;
}

.btn {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 0.8rem 1.5rem;
    margin-top: 1rem;
    text-decoration: none;
    border-radius: 5px;
}

.services {
    padding: 3rem 0;
    text-align: center;
}

/* 更多样式... */