/**
 * 表单通用逻辑组合式函数
 */

import { ref, reactive } from 'vue'
import type { Ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { REGEX } from '@/constants'

export interface UseFormReturn {
  formRef: Ref<FormInstance | undefined>
  loading: Ref<boolean>
  validate: () => Promise<boolean>
  resetForm: () => void
  clearValidate: () => void
}

export function useForm(): UseFormReturn {
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 验证表单
  const validate = async (): Promise<boolean> => {
    if (!formRef.value) return false
    
    try {
      await formRef.value.validate()
      return true
    } catch (error) {
      return false
    }
  }

  // 重置表单
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  // 清除验证
  const clearValidate = () => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }

  return {
    formRef,
    loading,
    validate,
    resetForm,
    clearValidate
  }
}

// 常用验证规则
export const commonRules: FormRules = {
  required: [
    { required: true, message: '此字段为必填项', trigger: 'blur' }
  ],
  
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: REGEX.PHONE, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { pattern: REGEX.EMAIL, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  
  idCard: [
    { pattern: REGEX.ID_CARD, message: '请输入正确的身份证号码', trigger: 'blur' }
  ],
  
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', min: 1, max: 120, message: '年龄必须在 1 到 120 之间', trigger: 'blur' }
  ]
}

// 自定义验证器
export const validators = {
  // 确认密码验证
  confirmPassword: (password: string) => {
    return (rule: any, value: string, callback: Function) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
  },
  
  // 手机号验证
  phone: (rule: any, value: string, callback: Function) => {
    if (value && !REGEX.PHONE.test(value)) {
      callback(new Error('请输入正确的手机号码'))
    } else {
      callback()
    }
  },
  
  // 邮箱验证
  email: (rule: any, value: string, callback: Function) => {
    if (value && !REGEX.EMAIL.test(value)) {
      callback(new Error('请输入正确的邮箱地址'))
    } else {
      callback()
    }
  },
  
  // 身份证验证
  idCard: (rule: any, value: string, callback: Function) => {
    if (value && !REGEX.ID_CARD.test(value)) {
      callback(new Error('请输入正确的身份证号码'))
    } else {
      callback()
    }
  }
}
