version: '3.8'

services:
  # 开发环境前端
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8080/api
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
    restart: unless-stopped
    networks:
      - health-butler-dev-network

  # 开发数据库
  db-dev:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=health_butler_dev
      - POSTGRES_USER=dev_user
      - POSTGRES_PASSWORD=dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    restart: unless-stopped
    networks:
      - health-butler-dev-network

  # 开发Redis
  redis-dev:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped
    networks:
      - health-butler-dev-network

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  health-butler-dev-network:
    driver: bridge
