// 工作提醒相关类型定义

export interface WorkReminder {
  id: number
  butlerId: number
  reminderType: number // 1-处方单 2-回访 3-订单 4-问卷 5-咨询 6-异常
  title: string
  content: string
  relatedId?: number // 关联业务ID
  priority: number // 1-紧急 2-重要 3-普通 4-信息
  status: number // 0-未读 1-已读 2-处理中 3-已完成 4-已延期
  isProcessed: boolean
  processedAt?: string
  dueTime?: string
  repeatCount: number
  lastRemindAt?: string
  createdAt: string
  updatedAt: string
}

export interface ReminderSettings {
  id: number
  butlerId: number
  browserNotify: boolean
  soundNotify: boolean
  wechatPush: boolean
  smsNotify: boolean
  emailNotify: boolean
  appPush: boolean
  voiceNotify: boolean
  workHoursStart: string
  workHoursEnd: string
  dndEnabled: boolean
  dndStartTime: string
  dndEndTime: string
  weekendDnd: boolean
  holidayDnd: boolean
  locationDnd: boolean
  emergencyException: boolean
  smartOptimize: boolean
  workloadBalance: boolean
  contextAware: boolean
  reminderAggregate: boolean
  aggregateWindow: number
  maxAggregateCount: number
  learningEnabled: boolean
}

export interface ReminderTemplate {
  id: number
  butlerId?: number
  templateName: string
  templateType: number // 1-快速回复 2-自动回复 3-提醒内容
  templateContent: string
  variables?: string[]
  isSystem: boolean
  isActive: boolean
  usageCount: number
  createdAt: string
}

export interface ReminderStatistics {
  totalReminders: number
  unreadReminders: number
  urgentReminders: number
  overdueReminders: number
  avgResponseTime: number // 分钟
  avgProcessTime: number // 分钟
  efficiencyScore: number
  byType: Record<string, number>
  byPriority: Record<string, number>
  byStatus: Record<string, number>
}

export interface ReminderSearchParams {
  type?: number
  priority?: number
  status?: number
  dateFrom?: string
  dateTo?: string
  keyword?: string
  page: number
  limit: number
}

export interface ReminderBatchOperation {
  action: 'mark_read' | 'mark_unread' | 'delete' | 'assign' | 'set_priority'
  reminderIds: number[]
  params?: {
    assigneeId?: number
    priority?: number
  }
}
