// 统一导出所有类型定义

export * from './user'
export * from './member'
export * from './reminder'

// 通用类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface PaginationResponse<T = any> {
  list: T[]
  total: number
  page: number
  limit: number
}

export interface SelectOption {
  label: string
  value: string | number
}

export interface FileUploadResponse {
  url: string
  name: string
  size: number
}
