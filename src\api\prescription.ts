/**
 * 处方单相关API接口
 */

import { request } from '@/utils/request'
import type {
  Prescription,
  PrescriptionExecution,
  PrescriptionQuery,
  PrescriptionStats,
  CreatePrescriptionParams,
  UpdatePrescriptionParams,
  ExecutePrescriptionParams
} from '@/types/prescription'
import type { ApiResponse, PaginationResult } from '@/types/api'

/**
 * 获取处方单列表
 */
export const getPrescriptionList = (params: PrescriptionQuery = {}) => {
  return request<PaginationResult<Prescription>>({
    url: '/prescriptions',
    method: 'GET',
    params
  })
}

/**
 * 获取处方单详情
 */
export const getPrescriptionDetail = (id: number) => {
  return request<Prescription>({
    url: `/prescriptions/${id}`,
    method: 'GET'
  })
}

/**
 * 创建处方单
 */
export const createPrescription = (data: CreatePrescriptionParams) => {
  return request<Prescription>({
    url: '/prescriptions',
    method: 'POST',
    data
  })
}

/**
 * 更新处方单
 */
export const updatePrescription = (id: number, data: UpdatePrescriptionParams) => {
  return request<Prescription>({
    url: `/prescriptions/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除处方单
 */
export const deletePrescription = (id: number) => {
  return request<void>({
    url: `/prescriptions/${id}`,
    method: 'DELETE'
  })
}

/**
 * 执行处方单
 */
export const executePrescription = (id: number, data: ExecutePrescriptionParams) => {
  return request<PrescriptionExecution>({
    url: `/prescriptions/${id}/execute`,
    method: 'POST',
    data
  })
}

/**
 * 获取处方单执行记录
 */
export const getPrescriptionExecutions = (prescriptionId: number) => {
  return request<PrescriptionExecution[]>({
    url: `/prescriptions/${prescriptionId}/executions`,
    method: 'GET'
  })
}

/**
 * 批量更新处方单状态
 */
export const batchUpdatePrescriptionStatus = (ids: number[], status: number) => {
  return request<void>({
    url: '/prescriptions/batch-status',
    method: 'PUT',
    data: { ids, status }
  })
}

/**
 * 获取处方单统计信息
 */
export const getPrescriptionStats = () => {
  return request<PrescriptionStats>({
    url: '/prescriptions/stats',
    method: 'GET'
  })
}

/**
 * 获取即将过期的处方单
 */
export const getExpiringPrescriptions = (hours: number = 24) => {
  return request<Prescription[]>({
    url: '/prescriptions/expiring',
    method: 'GET',
    params: { hours }
  })
}

/**
 * 获取紧急处方单
 */
export const getUrgentPrescriptions = () => {
  return request<Prescription[]>({
    url: '/prescriptions/urgent',
    method: 'GET'
  })
}

/**
 * 导出处方单数据
 */
export const exportPrescriptions = (params: PrescriptionQuery = {}) => {
  return request<Blob>({
    url: '/prescriptions/export',
    method: 'GET',
    params,
    responseType: 'blob'
  })
}

/**
 * 上传处方单附件
 */
export const uploadPrescriptionAttachment = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request<{ url: string }>({
    url: '/prescriptions/upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
