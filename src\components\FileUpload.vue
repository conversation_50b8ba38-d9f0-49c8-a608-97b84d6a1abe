<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :class="uploadClass"
      :drag="drag"
      :multiple="multiple"
      :accept="accept"
      :show-file-list="showFileList"
      :auto-upload="autoUpload"
      :limit="limit"
      :file-list="fileList"
      :before-upload="handleBeforeUpload"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :http-request="customUpload"
    >
      <template #trigger>
        <slot name="trigger">
          <el-button type="primary" :icon="Upload">
            {{ triggerText }}
          </el-button>
        </slot>
      </template>
      
      <template #tip>
        <div class="upload-tip">
          <slot name="tip">
            <div class="tip-content">
              <p>{{ tipText }}</p>
              <p v-if="maxSize" class="size-limit">
                文件大小不超过 {{ formatFileSize(maxSize) }}
              </p>
              <p v-if="allowedTypes.length > 0" class="type-limit">
                支持格式：{{ allowedTypes.join(', ') }}
              </p>
            </div>
          </slot>
        </div>
      </template>
      
      <template #default>
        <div v-if="drag" class="drag-area">
          <el-icon class="upload-icon"><UploadFilled /></el-icon>
          <div class="upload-text">
            <p>{{ dragText }}</p>
            <p class="upload-hint">或 <em>点击上传</em></p>
          </div>
        </div>
      </template>
    </el-upload>

    <!-- 上传进度 -->
    <div v-if="uploadingFiles.size > 0" class="upload-progress">
      <div class="progress-header">
        <span>正在上传 {{ uploadingFiles.size }} 个文件</span>
        <el-button type="text" @click="cancelAllUploads">取消全部</el-button>
      </div>
      <div class="progress-list">
        <div v-for="[fileId, progress] in uploadingFiles" :key="fileId" class="progress-item">
          <div class="file-info">
            <el-icon><Document /></el-icon>
            <span class="file-name">{{ progress.fileName }}</span>
          </div>
          <div class="progress-bar">
            <el-progress
              :percentage="progress.percentage"
              :status="progress.percentage === 100 ? 'success' : undefined"
              :stroke-width="6"
            />
          </div>
          <div class="progress-actions">
            <span class="progress-text">{{ progress.percentage }}%</span>
            <el-button type="text" size="small" @click="cancelUpload(fileId)">
              取消
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览 -->
    <div v-if="showPreview && uploadedFiles.length > 0" class="file-preview">
      <div class="preview-header">
        <span>已上传文件 ({{ uploadedFiles.length }})</span>
        <el-button v-if="allowClear" type="text" @click="clearFiles">清空</el-button>
      </div>
      <div class="preview-list">
        <div v-for="file in uploadedFiles" :key="file.id" class="preview-item">
          <div class="file-thumbnail">
            <el-image
              v-if="file.type === 'image' && file.thumbnailUrl"
              :src="file.thumbnailUrl"
              :preview-src-list="[file.url]"
              fit="cover"
              class="thumbnail-image"
            />
            <div v-else class="file-icon">
              <el-icon><Document /></el-icon>
            </div>
          </div>
          <div class="file-details">
            <div class="file-name" :title="file.originalName">
              {{ file.originalName }}
            </div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
              <span class="file-type">{{ file.mimeType }}</span>
            </div>
            <div v-if="file.tags && file.tags.length > 0" class="file-tags">
              <el-tag v-for="tag in file.tags" :key="tag" size="small">
                {{ tag }}
              </el-tag>
            </div>
          </div>
          <div class="file-actions">
            <el-button type="text" size="small" @click="previewFile(file)">
              预览
            </el-button>
            <el-button type="text" size="small" @click="downloadFile(file)">
              下载
            </el-button>
            <el-button type="text" size="small" @click="editFile(file)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="removeFile(file.id)">
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, UploadFilled, Document } from '@element-plus/icons-vue'
import { useFileStore } from '@/stores/file'
import { formatFileSize } from '@/utils'
import type { 
  FileInfo, 
  FileUploadParams, 
  UploadProgress,
  FileType 
} from '@/types/file'
import type { UploadFile, UploadFiles, UploadRequestOptions } from 'element-plus'

interface Props {
  // 基本配置
  drag?: boolean                    // 是否启用拖拽上传
  multiple?: boolean                // 是否支持多选
  autoUpload?: boolean              // 是否自动上传
  showFileList?: boolean            // 是否显示文件列表
  showPreview?: boolean             // 是否显示预览
  allowClear?: boolean              // 是否允许清空
  
  // 限制配置
  limit?: number                    // 文件数量限制
  maxSize?: number                  // 文件大小限制(字节)
  allowedTypes?: string[]           // 允许的文件类型
  
  // 上传配置
  category?: string                 // 文件分类
  memberId?: number                 // 关联会员ID
  relatedType?: string              // 关联类型
  relatedId?: number                // 关联ID
  generateThumbnail?: boolean       // 是否生成缩略图
  compressImage?: boolean           // 是否压缩图片
  watermark?: boolean               // 是否添加水印
  
  // 文本配置
  triggerText?: string              // 触发按钮文本
  dragText?: string                 // 拖拽区域文本
  tipText?: string                  // 提示文本
  
  // 样式配置
  uploadClass?: string              // 上传组件样式类
}

const props = withDefaults(defineProps<Props>(), {
  drag: false,
  multiple: false,
  autoUpload: true,
  showFileList: false,
  showPreview: true,
  allowClear: true,
  limit: 10,
  maxSize: 100 * 1024 * 1024, // 100MB
  allowedTypes: () => [],
  generateThumbnail: true,
  compressImage: false,
  watermark: false,
  triggerText: '选择文件',
  dragText: '将文件拖到此处',
  tipText: '请选择要上传的文件'
})

interface Emits {
  (e: 'success', files: FileInfo[]): void
  (e: 'error', error: any): void
  (e: 'progress', progress: UploadProgress): void
  (e: 'remove', fileId: string): void
}

const emit = defineEmits<Emits>()

const fileStore = useFileStore()
const uploadRef = ref()
const fileList = ref<UploadFile[]>([])
const uploadedFiles = ref<FileInfo[]>([])

// 计算属性
const accept = computed(() => {
  if (props.allowedTypes.length === 0) return undefined
  return props.allowedTypes.map(type => `.${type}`).join(',')
})

const uploadingFiles = computed(() => fileStore.uploadingFiles)

// 上传前验证
const handleBeforeUpload = async (file: File) => {
  // 文件大小验证
  if (props.maxSize && file.size > props.maxSize) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(props.maxSize)}`)
    return false
  }
  
  // 文件类型验证
  if (props.allowedTypes.length > 0) {
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (!fileExtension || !props.allowedTypes.includes(fileExtension)) {
      ElMessage.error(`不支持的文件类型，请选择 ${props.allowedTypes.join(', ')} 格式的文件`)
      return false
    }
  }
  
  try {
    // 调用API验证
    const validation = await fileStore.validateFileAction(file, {
      file,
      maxSize: props.maxSize,
      allowedTypes: props.allowedTypes
    })
    
    if (!validation.isValid) {
      ElMessage.error(validation.errors.join(', '))
      return false
    }
    
    if (validation.warnings.length > 0) {
      ElMessage.warning(validation.warnings.join(', '))
    }
  } catch (error) {
    console.error('文件验证失败:', error)
  }
  
  return true
}

// 自定义上传
const customUpload = async (options: UploadRequestOptions) => {
  try {
    const uploadParams: FileUploadParams = {
      file: options.file,
      category: props.category,
      memberId: props.memberId,
      relatedType: props.relatedType,
      relatedId: props.relatedId,
      generateThumbnail: props.generateThumbnail,
      compressImage: props.compressImage,
      watermark: props.watermark
    }
    
    const fileInfo = await fileStore.uploadFileAction(uploadParams)
    uploadedFiles.value.push(fileInfo)
    emit('success', [fileInfo])
    
    return fileInfo
  } catch (error) {
    emit('error', error)
    throw error
  }
}

// 上传进度
const handleProgress = (event: any, file: UploadFile) => {
  const progress: UploadProgress = {
    fileId: file.uid || '',
    fileName: file.name,
    loaded: event.loaded || 0,
    total: event.total || 0,
    percentage: Math.round(((event.loaded || 0) / (event.total || 1)) * 100)
  }
  emit('progress', progress)
}

// 上传成功
const handleSuccess = (response: any, file: UploadFile) => {
  // 由 customUpload 处理
}

// 上传失败
const handleError = (error: any, file: UploadFile) => {
  ElMessage.error(`文件 ${file.name} 上传失败`)
  emit('error', error)
}

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = uploadedFiles.value.findIndex(f => f.name === file.name)
  if (index !== -1) {
    const fileInfo = uploadedFiles.value[index]
    uploadedFiles.value.splice(index, 1)
    emit('remove', fileInfo.id)
  }
}

// 超出限制
const handleExceed = (files: File[]) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
}

// 取消上传
const cancelUpload = (fileId: string) => {
  // 实现取消上传逻辑
  uploadingFiles.value.delete(fileId)
}

// 取消全部上传
const cancelAllUploads = () => {
  uploadingFiles.value.clear()
}

// 清空文件
const clearFiles = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有文件吗？', '确认清空', {
      type: 'warning'
    })
    
    uploadedFiles.value = []
    fileList.value = []
    uploadRef.value?.clearFiles()
  } catch (error) {
    // 用户取消
  }
}

// 预览文件
const previewFile = (file: FileInfo) => {
  if (file.type === 'image') {
    // 图片预览
    window.open(file.url, '_blank')
  } else {
    // 其他文件类型的预览
    ElMessage.info('暂不支持此文件类型的预览')
  }
}

// 下载文件
const downloadFile = async (file: FileInfo) => {
  try {
    await fileStore.downloadFileAction(file.id, file.originalName)
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

// 编辑文件
const editFile = (file: FileInfo) => {
  // 实现文件编辑逻辑
  ElMessage.info('编辑功能开发中')
}

// 删除文件
const removeFile = async (fileId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '确认删除', {
      type: 'warning'
    })
    
    await fileStore.deleteFileAction(fileId)
    const index = uploadedFiles.value.findIndex(f => f.id === fileId)
    if (index !== -1) {
      uploadedFiles.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
    emit('remove', fileId)
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 监听上传的文件变化
watch(() => props.limit, (newLimit) => {
  if (uploadRef.value) {
    uploadRef.value.limit = newLimit
  }
})

// 暴露方法
defineExpose({
  clearFiles,
  uploadedFiles: uploadedFiles.value
})
</script>

<style scoped>
.file-upload {
  width: 100%;
}

.upload-tip {
  margin-top: 10px;
}

.tip-content p {
  margin: 5px 0;
  color: var(--text-color-secondary);
  font-size: 14px;
}

.size-limit,
.type-limit {
  color: var(--text-color-placeholder);
  font-size: 12px;
}

.drag-area {
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  font-size: 48px;
  color: var(--text-color-placeholder);
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: var(--text-color-regular);
}

.upload-hint {
  color: var(--text-color-secondary);
  font-size: 14px;
}

.upload-hint em {
  color: var(--primary-color);
  font-style: normal;
}

.upload-progress {
  margin-top: 20px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.file-name {
  font-size: 14px;
  color: var(--text-color-regular);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progress-bar {
  flex: 1;
}

.progress-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: var(--text-color-secondary);
  min-width: 40px;
}

.file-preview {
  margin-top: 20px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--border-color-light);
  border-radius: 6px;
  margin-bottom: 8px;
}

.file-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-light);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.file-icon {
  font-size: 24px;
  color: var(--text-color-placeholder);
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-bottom: 4px;
}

.file-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.file-actions {
  display: flex;
  gap: 8px;
}
</style>
