<template>
  <div class="statistics-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">📊 统计分析</h1>
        <p class="page-desc">查看工作量统计和数据分析报表</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
        />
        <el-button :icon="Download" @click="exportReport">
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="metric-card members">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-number">{{ metrics.totalMembers }}</div>
                <div class="metric-label">管理会员</div>
                <div class="metric-change positive">
                  +{{ metrics.newMembers }} 新增
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="metric-card visits">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><LocationInformation /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-number">{{ metrics.totalVisits }}</div>
                <div class="metric-label">完成回访</div>
                <div class="metric-change positive">
                  {{ metrics.visitCompletionRate }}% 完成率
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="metric-card questionnaires">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-number">{{ metrics.questionnairesSent }}</div>
                <div class="metric-label">问卷推送</div>
                <div class="metric-change positive">
                  {{ metrics.questionnaireCompletionRate }}% 完成率
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="metric-card prescriptions">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><Notebook /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-number">{{ metrics.prescriptionsHandled }}</div>
                <div class="metric-label">处方执行</div>
                <div class="metric-change positive">
                  {{ metrics.prescriptionCompletionRate }}% 完成率
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 工作量趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card shadow="never" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>📈 工作量趋势</span>
              <el-select v-model="trendMetric" size="small" style="width: 120px">
                <el-option label="回访数量" value="visits" />
                <el-option label="问卷推送" value="questionnaires" />
                <el-option label="处方执行" value="prescriptions" />
              </el-select>
            </div>
          </template>
          
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon size="48" color="#ccc"><TrendCharts /></el-icon>
              <p>工作量趋势图</p>
              <div class="mock-data">
                <div class="trend-item">
                  <span class="trend-date">本周</span>
                  <span class="trend-value">{{ getTrendValue('week') }}</span>
                </div>
                <div class="trend-item">
                  <span class="trend-date">本月</span>
                  <span class="trend-value">{{ getTrendValue('month') }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 工作分布饼图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card shadow="never" class="chart-card">
          <template #header>
            <span>🥧 工作分布</span>
          </template>
          
          <div class="chart-container">
            <div class="pie-chart-placeholder">
              <div class="pie-legend">
                <div class="legend-item">
                  <span class="legend-color visits"></span>
                  <span class="legend-label">回访 (40%)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color questionnaires"></span>
                  <span class="legend-label">问卷 (25%)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color prescriptions"></span>
                  <span class="legend-label">处方 (20%)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color consultations"></span>
                  <span class="legend-label">咨询 (15%)</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <div class="table-section">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span>📋 详细统计</span>
            <el-radio-group v-model="tableView" size="small">
              <el-radio-button label="daily">按日统计</el-radio-button>
              <el-radio-button label="weekly">按周统计</el-radio-button>
              <el-radio-button label="monthly">按月统计</el-radio-button>
            </el-radio-group>
          </div>
        </template>

        <el-table :data="statisticsData" stripe>
          <el-table-column 
            :label="getTableDateLabel()" 
            prop="date" 
            width="120"
          />
          
          <el-table-column label="新增会员" prop="newMembers" width="100">
            <template #default="{ row }">
              <span class="number-cell">{{ row.newMembers }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="完成回访" prop="visits" width="100">
            <template #default="{ row }">
              <span class="number-cell">{{ row.visits }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="问卷推送" prop="questionnaires" width="100">
            <template #default="{ row }">
              <span class="number-cell">{{ row.questionnaires }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="处方执行" prop="prescriptions" width="100">
            <template #default="{ row }">
              <span class="number-cell">{{ row.prescriptions }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="会员咨询" prop="consultations" width="100">
            <template #default="{ row }">
              <span class="number-cell">{{ row.consultations }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="工作效率" prop="efficiency" width="100">
            <template #default="{ row }">
              <el-progress 
                :percentage="row.efficiency" 
                :stroke-width="8"
                :show-text="false"
              />
              <span class="efficiency-text">{{ row.efficiency }}%</span>
            </template>
          </el-table-column>
          
          <el-table-column label="总工作量" prop="totalWorkload" width="100">
            <template #default="{ row }">
              <span class="number-cell total">{{ row.totalWorkload }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 健康指标统计 -->
    <div class="health-metrics-section">
      <el-card shadow="never">
        <template #header>
          <span>🏥 健康指标统计</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="health-metric-item">
              <div class="metric-title">血压异常</div>
              <div class="metric-value danger">{{ healthMetrics.abnormalBloodPressure }}</div>
              <div class="metric-desc">需要重点关注</div>
            </div>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="health-metric-item">
              <div class="metric-title">血糖异常</div>
              <div class="metric-value warning">{{ healthMetrics.abnormalBloodSugar }}</div>
              <div class="metric-desc">需要监测</div>
            </div>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="health-metric-item">
              <div class="metric-title">体重超标</div>
              <div class="metric-value info">{{ healthMetrics.overweight }}</div>
              <div class="metric-desc">生活方式调整</div>
            </div>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="health-metric-item">
              <div class="metric-title">指标正常</div>
              <div class="metric-value success">{{ healthMetrics.normal }}</div>
              <div class="metric-desc">继续保持</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download,
  User,
  LocationInformation,
  Document,
  Notebook,
  TrendCharts
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const dateRange = ref<[string, string]>([
  dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
])

const trendMetric = ref('visits')
const tableView = ref('daily')

// 核心指标数据
const metrics = reactive({
  totalMembers: 156,
  newMembers: 8,
  totalVisits: 45,
  visitCompletionRate: 92,
  questionnairesSent: 32,
  questionnaireCompletionRate: 85,
  prescriptionsHandled: 28,
  prescriptionCompletionRate: 96
})

// 健康指标统计
const healthMetrics = reactive({
  abnormalBloodPressure: 12,
  abnormalBloodSugar: 8,
  overweight: 15,
  normal: 121
})

// 详细统计数据
const statisticsData = ref([
  {
    date: '2024-01-25',
    newMembers: 3,
    visits: 8,
    questionnaires: 5,
    prescriptions: 4,
    consultations: 12,
    efficiency: 88,
    totalWorkload: 32
  },
  {
    date: '2024-01-24',
    newMembers: 2,
    visits: 6,
    questionnaires: 8,
    prescriptions: 3,
    consultations: 10,
    efficiency: 92,
    totalWorkload: 29
  },
  {
    date: '2024-01-23',
    newMembers: 1,
    visits: 7,
    questionnaires: 4,
    prescriptions: 5,
    consultations: 8,
    efficiency: 85,
    totalWorkload: 25
  }
])

// 方法
const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    // 重新获取数据
    ElMessage.info('正在更新统计数据...')
  }
}

const getTrendValue = (period: string) => {
  const values = {
    visits: { week: 28, month: 125 },
    questionnaires: { week: 18, month: 85 },
    prescriptions: { week: 15, month: 68 }
  }
  return values[trendMetric.value as keyof typeof values]?.[period as keyof typeof values.visits] || 0
}

const getTableDateLabel = () => {
  const labels = {
    daily: '日期',
    weekly: '周次',
    monthly: '月份'
  }
  return labels[tableView.value as keyof typeof labels]
}

const exportReport = () => {
  ElMessage.success('报表导出功能开发中...')
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.statistics-container {
  padding: 20px;
  background: var(--bg-color);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.page-desc {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.overview-section {
  margin-bottom: 20px;
}

.metric-card {
  cursor: pointer;
  transition: transform 0.2s;
  border-radius: 12px;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.metric-card.members .metric-icon { background: var(--medical-blue); }
.metric-card.visits .metric-icon { background: var(--medical-green); }
.metric-card.questionnaires .metric-icon { background: var(--medical-teal); }
.metric-card.prescriptions .metric-icon { background: var(--warning-color); }

.metric-number {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin: 4px 0;
}

.metric-change {
  font-size: 12px;
}

.metric-change.positive {
  color: var(--medical-green);
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-placeholder p {
  margin: 12px 0 20px 0;
  font-size: 14px;
}

.mock-data {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.trend-date {
  font-size: 12px;
  color: #999;
}

.trend-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--medical-blue);
}

.pie-chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.visits { background: var(--medical-green); }
.legend-color.questionnaires { background: var(--medical-blue); }
.legend-color.prescriptions { background: var(--warning-color); }
.legend-color.consultations { background: var(--medical-teal); }

.legend-label {
  font-size: 14px;
  color: #666;
}

.table-section {
  margin-bottom: 20px;
}

.number-cell {
  font-weight: 500;
  color: #333;
}

.number-cell.total {
  color: var(--medical-blue);
  font-weight: 600;
}

.efficiency-text {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.health-metrics-section {
  margin-bottom: 20px;
}

.health-metric-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: white;
  border: 1px solid var(--border-color);
}

.metric-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.metric-value.danger { color: var(--danger-color); }
.metric-value.warning { color: var(--warning-color); }
.metric-value.info { color: var(--info-color); }
.metric-value.success { color: var(--medical-green); }

.metric-desc {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .metric-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .chart-card {
    height: 300px;
  }
  
  .mock-data {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
