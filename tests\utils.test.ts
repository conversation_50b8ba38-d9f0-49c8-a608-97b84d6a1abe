/**
 * 工具函数测试示例
 * 注意：这是一个示例文件，实际项目中需要配置测试框架如 Vitest 或 Jest
 */

import { describe, it, expect } from 'vitest'
import { formatDate, formatFileSize, validatePhone } from '@/utils'

describe('工具函数测试', () => {
  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = '2024-01-25T10:30:00Z'
      expect(formatDate(date)).toBe('2024-01-25')
    })

    it('应该支持自定义格式', () => {
      const date = '2024-01-25T10:30:00Z'
      expect(formatDate(date, 'YYYY年MM月DD日')).toBe('2024年01月25日')
    })
  })

  describe('formatFileSize', () => {
    it('应该正确格式化文件大小', () => {
      expect(formatFileSize(0)).toBe('0 B')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1024 * 1024)).toBe('1 MB')
    })
  })

  describe('validatePhone', () => {
    it('应该验证有效的手机号', () => {
      expect(validatePhone('13800138000')).toBe(true)
      expect(validatePhone('15912345678')).toBe(true)
    })

    it('应该拒绝无效的手机号', () => {
      expect(validatePhone('12345678901')).toBe(false)
      expect(validatePhone('1380013800')).toBe(false)
      expect(validatePhone('abc')).toBe(false)
    })
  })
})
