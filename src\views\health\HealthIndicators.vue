<template>
  <div class="health-indicators">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>健康指标管理</h2>
        <p>管理会员健康指标数据和异常预警</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          录入指标
        </el-button>
        <el-button type="success" :icon="Upload" @click="showImportDialog = true">
          批量导入
        </el-button>
        <el-button type="info" :icon="Download" @click="exportData">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.totalRecords || 0 }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon abnormal">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.abnormalCount || 0 }}</div>
              <div class="stat-label">异常记录</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.todayRecords || 0 }}</div>
              <div class="stat-label">今日录入</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats?.abnormalRate || 0 }}%</div>
              <div class="stat-label">异常率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 异常预警 -->
    <div v-if="criticalAlerts.length > 0" class="alert-section">
      <el-alert
        title="健康异常预警"
        type="error"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="alert-content">
            <p>发现 {{ criticalAlerts.length }} 个危急异常指标，请立即处理：</p>
            <div class="alert-list">
              <div v-for="alert in criticalAlerts.slice(0, 3)" :key="alert.id" class="alert-item">
                <span class="member-name">{{ alert.memberName }}</span>
                <span class="indicator-type">{{ getIndicatorName(alert.indicatorType) }}</span>
                <span class="alert-value">{{ alert.value }}</span>
                <el-button type="danger" size="small" @click="handleAlert(alert.id)">
                  处理
                </el-button>
              </div>
            </div>
            <el-button v-if="criticalAlerts.length > 3" type="text" @click="viewAllAlerts">
              查看全部 {{ criticalAlerts.length }} 个预警
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <el-form :model="searchForm" inline>
        <el-form-item label="指标类型">
          <el-select v-model="searchForm.indicatorType" placeholder="全部指标" clearable>
            <el-option label="血压" value="blood_pressure" />
            <el-option label="血糖" value="blood_sugar" />
            <el-option label="血脂" value="blood_lipid" />
            <el-option label="心率" value="heart_rate" />
            <el-option label="体重" value="weight" />
            <el-option label="BMI" value="bmi" />
            <el-option label="体温" value="temperature" />
            <el-option label="血氧饱和度" value="oxygen_saturation" />
          </el-select>
        </el-form-item>
        <el-form-item label="异常等级">
          <el-select v-model="searchForm.abnormalLevel" placeholder="全部等级" clearable>
            <el-option label="正常" :value="0" />
            <el-option label="轻度异常" :value="1" />
            <el-option label="中度异常" :value="2" />
            <el-option label="重度异常" :value="3" />
            <el-option label="危急" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="会员姓名">
          <el-input v-model="searchForm.memberName" placeholder="请输入会员姓名" clearable />
        </el-form-item>
        <el-form-item label="测量时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 健康记录列表 -->
    <div class="health-table">
      <el-table
        v-loading="loading"
        :data="healthRecords"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="memberName" label="会员姓名" width="100" />
        <el-table-column label="指标类型" width="120">
          <template #default="{ row }">
            {{ getIndicatorName(row.indicatorType) }}
          </template>
        </el-table-column>
        <el-table-column prop="value" label="指标值" width="100">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.isAbnormal }">
              {{ row.value }} {{ row.unit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="referenceRange" label="参考范围" width="120" />
        <el-table-column label="异常等级" width="100">
          <template #default="{ row }">
            <el-tag :type="getAbnormalLevelColor(row.abnormalLevel)">
              {{ getAbnormalLevelText(row.abnormalLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="measuredAt" label="测量时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.measuredAt, 'YYYY-MM-DD HH:mm') }}
          </template>
        </el-table-column>
        <el-table-column prop="recordedBy" label="记录人" width="100" />
        <el-table-column prop="deviceInfo" label="测量设备" width="120" show-overflow-tooltip />
        <el-table-column prop="notes" label="备注" show-overflow-tooltip />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row.id)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="editRecord(row.id)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteRecord(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建健康记录对话框 -->
    <HealthRecordCreateDialog
      v-model="showCreateDialog"
      @success="handleCreateSuccess"
    />

    <!-- 批量导入对话框 -->
    <HealthImportDialog
      v-model="showImportDialog"
      @success="handleImportSuccess"
    />

    <!-- 异常预警处理对话框 -->
    <HealthAlertDialog
      v-model="showAlertDialog"
      :alert-id="currentAlertId"
      @success="handleAlertSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Upload, 
  Download, 
  DataAnalysis, 
  Warning, 
  Calendar, 
  TrendCharts 
} from '@element-plus/icons-vue'
import { useHealthStore } from '@/stores/health'
import { formatDate } from '@/utils'
import HealthRecordCreateDialog from './components/HealthRecordCreateDialog.vue'
import HealthImportDialog from './components/HealthImportDialog.vue'
import HealthAlertDialog from './components/HealthAlertDialog.vue'
import type { HealthRecord, HealthQuery, HealthIndicatorType, AbnormalLevel } from '@/types/health'

const healthStore = useHealthStore()

// 响应式数据
const showCreateDialog = ref(false)
const showImportDialog = ref(false)
const showAlertDialog = ref(false)
const currentAlertId = ref<number | null>(null)
const selectedRecords = ref<HealthRecord[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const dateRange = ref<[string, string] | null>(null)

const searchForm = reactive({
  indicatorType: undefined,
  abnormalLevel: undefined,
  memberName: '',
  startDate: '',
  endDate: ''
})

// 计算属性
const healthRecords = computed(() => healthStore.healthRecords)
const loading = computed(() => healthStore.loading)
const total = computed(() => healthStore.total)
const stats = computed(() => healthStore.stats)
const criticalAlerts = computed(() => healthStore.criticalAlerts)

// 方法
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    indicatorType: undefined,
    abnormalLevel: undefined,
    memberName: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = null
  handleSearch()
}

const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.startDate = dates[0]
    searchForm.endDate = dates[1]
  } else {
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
}

const fetchData = () => {
  healthStore.fetchHealthRecords({
    page: currentPage.value,
    limit: pageSize.value,
    ...searchForm
  })
}

const handleSelectionChange = (selection: HealthRecord[]) => {
  selectedRecords.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchData()
}

const viewDetail = (id: number) => {
  // 跳转到详情页面或显示详情对话框
  console.log('查看详情:', id)
}

const editRecord = (id: number) => {
  // 编辑记录
  console.log('编辑记录:', id)
}

const deleteRecord = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条健康记录吗？删除后无法恢复。', '确认删除', {
      type: 'warning'
    })
    
    await healthStore.deleteHealthRecordAction(id)
    ElMessage.success('健康记录已删除')
    fetchData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleAlert = (alertId: number) => {
  currentAlertId.value = alertId
  showAlertDialog.value = true
}

const viewAllAlerts = () => {
  // 跳转到预警列表页面
  console.log('查看全部预警')
}

const exportData = async () => {
  try {
    await healthStore.exportHealthDataAction(searchForm)
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  }
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  fetchData()
}

const handleImportSuccess = () => {
  showImportDialog.value = false
  fetchData()
}

const handleAlertSuccess = () => {
  showAlertDialog.value = false
  healthStore.fetchHealthAlerts()
}

// 辅助方法
const getIndicatorName = (type: HealthIndicatorType) => {
  const names = {
    blood_pressure: '血压',
    blood_sugar: '血糖',
    blood_lipid: '血脂',
    heart_rate: '心率',
    weight: '体重',
    bmi: 'BMI',
    temperature: '体温',
    oxygen_saturation: '血氧饱和度',
    cholesterol: '胆固醇',
    uric_acid: '尿酸',
    creatinine: '肌酐',
    hemoglobin: '血红蛋白'
  }
  return names[type] || type
}

const getAbnormalLevelColor = (level: AbnormalLevel) => {
  const colors = ['success', 'warning', 'warning', 'danger', 'danger']
  return colors[level] || 'info'
}

const getAbnormalLevelText = (level: AbnormalLevel) => {
  const texts = ['正常', '轻度异常', '中度异常', '重度异常', '危急']
  return texts[level] || '未知'
}

// 生命周期
onMounted(() => {
  fetchData()
  healthStore.fetchHealthStats()
  healthStore.fetchHealthAlerts()
  healthStore.fetchIndicatorConfigs()
})
</script>

<style scoped>
.health-indicators {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: var(--text-color-primary);
}

.header-left p {
  margin: 0;
  color: var(--text-color-secondary);
}

.header-right {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.stat-icon.total { background: var(--primary-color); }
.stat-icon.abnormal { background: var(--danger-color); }
.stat-icon.today { background: var(--success-color); }
.stat-icon.rate { background: var(--warning-color); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-secondary);
}

.alert-section {
  margin-bottom: 20px;
}

.alert-content {
  margin-top: 10px;
}

.alert-list {
  margin: 10px 0;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 8px;
}

.member-name {
  font-weight: 600;
  color: var(--text-color-primary);
}

.indicator-type {
  color: var(--text-color-regular);
}

.alert-value {
  color: var(--danger-color);
  font-weight: 600;
}

.search-filters {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.health-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.text-danger {
  color: var(--danger-color);
  font-weight: 600;
}
</style>
