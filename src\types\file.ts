/**
 * 文件管理相关类型定义
 */

// 文件类型
export enum FileType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document'
}

// 文件状态
export enum FileStatus {
  UPLOADING = 1,    // 上传中
  UPLOADED = 2,     // 已上传
  FAILED = 3,       // 上传失败
  PROCESSING = 4,   // 处理中
  PROCESSED = 5     // 处理完成
}

// 文件信息
export interface FileInfo {
  id: string
  name: string                    // 文件名
  originalName: string            // 原始文件名
  type: FileType                  // 文件类型
  mimeType: string               // MIME类型
  size: number                   // 文件大小(字节)
  url: string                    // 文件URL
  thumbnailUrl?: string          // 缩略图URL
  previewUrl?: string            // 预览URL
  status: FileStatus             // 文件状态
  
  // 元数据
  width?: number                 // 图片/视频宽度
  height?: number                // 图片/视频高度
  duration?: number              // 音视频时长(秒)
  
  // 分类和标签
  category?: string              // 文件分类
  tags?: string[]                // 文件标签
  description?: string           // 文件描述
  
  // 关联信息
  memberId?: number              // 关联会员ID
  memberName?: string            // 关联会员姓名
  relatedType?: string           // 关联类型(visit/prescription/health等)
  relatedId?: number             // 关联ID
  
  // 上传信息
  uploadedBy: string             // 上传人
  uploadProgress?: number        // 上传进度(0-100)
  uploadError?: string           // 上传错误信息
  
  createdAt: string
  updatedAt: string
}

// 文件上传参数
export interface FileUploadParams {
  file: File
  category?: string              // 文件分类
  tags?: string[]                // 文件标签
  description?: string           // 文件描述
  memberId?: number              // 关联会员ID
  relatedType?: string           // 关联类型
  relatedId?: number             // 关联ID
  
  // 上传配置
  maxSize?: number               // 最大文件大小(字节)
  allowedTypes?: string[]        // 允许的文件类型
  generateThumbnail?: boolean    // 是否生成缩略图
  compressImage?: boolean        // 是否压缩图片
  watermark?: boolean            // 是否添加水印
}

// 文件查询参数
export interface FileQuery {
  page?: number
  limit?: number
  type?: FileType
  status?: FileStatus
  category?: string
  memberId?: number
  memberName?: string
  relatedType?: string
  relatedId?: number
  keyword?: string
  startDate?: string
  endDate?: string
  tags?: string[]
}

// 文件统计信息
export interface FileStats {
  totalFiles: number             // 总文件数
  totalSize: number              // 总文件大小
  imageCount: number             // 图片数量
  videoCount: number             // 视频数量
  audioCount: number             // 音频数量
  documentCount: number          // 文档数量
  todayUploads: number           // 今日上传数
  weeklyUploads: number          // 本周上传数
  monthlyUploads: number         // 本月上传数
  
  // 按分类统计
  categoryStats: {
    category: string
    count: number
    size: number
  }[]
  
  // 按会员统计
  memberStats: {
    memberId: number
    memberName: string
    count: number
    size: number
  }[]
}

// 文件批量操作参数
export interface FileBatchParams {
  fileIds: string[]
  action: 'delete' | 'move' | 'tag' | 'category'
  targetCategory?: string        // 目标分类
  tags?: string[]                // 标签
}

// 文件分享信息
export interface FileShare {
  id: string
  fileId: string                 // 文件ID
  shareUrl: string               // 分享链接
  shareCode?: string             // 分享码
  password?: string              // 访问密码
  expiresAt?: string             // 过期时间
  downloadLimit?: number         // 下载次数限制
  downloadCount: number          // 已下载次数
  isActive: boolean              // 是否有效
  createdBy: string              // 创建人
  createdAt: string
}

// 文件压缩配置
export interface CompressionConfig {
  quality: number                // 压缩质量(0-100)
  maxWidth?: number              // 最大宽度
  maxHeight?: number             // 最大高度
  format?: 'jpeg' | 'png' | 'webp'  // 输出格式
}

// 文件水印配置
export interface WatermarkConfig {
  text?: string                  // 水印文字
  image?: string                 // 水印图片URL
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
  opacity: number                // 透明度(0-1)
  size?: number                  // 大小
}

// 文件预览配置
export interface PreviewConfig {
  width?: number                 // 预览宽度
  height?: number                // 预览高度
  quality?: number               // 预览质量
  format?: string                // 预览格式
}

// 上传进度回调
export interface UploadProgress {
  fileId: string
  fileName: string
  loaded: number                 // 已上传字节数
  total: number                  // 总字节数
  percentage: number             // 上传百分比
  speed?: number                 // 上传速度(字节/秒)
  remainingTime?: number         // 剩余时间(秒)
}

// 文件验证结果
export interface FileValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// 文件处理任务
export interface FileProcessTask {
  id: string
  fileId: string
  taskType: 'thumbnail' | 'compress' | 'watermark' | 'convert'
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  result?: any
  error?: string
  createdAt: string
  updatedAt: string
}
