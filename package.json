{"name": "health-butler-system", "version": "1.0.0", "description": "健康管家管理系统", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "axios": "^1.4.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "@element-plus/icons-vue": "^2.1.0", "dayjs": "^1.11.9", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/node": "^20.4.5", "@types/js-cookie": "^3.0.3", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-typescript": "^11.0.3", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "typescript": "^5.1.6", "vite": "^4.4.7", "vue-tsc": "^1.8.5"}}