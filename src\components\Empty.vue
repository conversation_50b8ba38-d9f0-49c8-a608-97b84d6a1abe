<template>
  <div class="empty-container">
    <div class="empty-content">
      <div class="empty-icon">
        <el-icon :size="iconSize" :color="iconColor">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      
      <div class="empty-text">
        <h3 class="empty-title">{{ title }}</h3>
        <p class="empty-description">{{ description }}</p>
      </div>
      
      <div v-if="showAction" class="empty-action">
        <slot name="action">
          <el-button type="primary" @click="handleAction">
            {{ actionText }}
          </el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  DocumentRemove, 
  User, 
  Bell, 
  Document, 
  DataAnalysis,
  Search
} from '@element-plus/icons-vue'

interface Props {
  type?: 'default' | 'member' | 'reminder' | 'questionnaire' | 'statistics' | 'search'
  title?: string
  description?: string
  iconSize?: number
  showAction?: boolean
  actionText?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  title: '暂无数据',
  description: '当前没有相关数据',
  iconSize: 80,
  showAction: false,
  actionText: '刷新'
})

const emit = defineEmits<{
  action: []
}>()

const iconComponent = computed(() => {
  const iconMap = {
    default: DocumentRemove,
    member: User,
    reminder: Bell,
    questionnaire: Document,
    statistics: DataAnalysis,
    search: Search
  }
  return iconMap[props.type]
})

const iconColor = computed(() => {
  const colorMap = {
    default: '#c0c4cc',
    member: '#409eff',
    reminder: '#e6a23c',
    questionnaire: '#67c23a',
    statistics: '#909399',
    search: '#f56c6c'
  }
  return colorMap[props.type]
})

const handleAction = () => {
  emit('action')
}
</script>

<style scoped>
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px 20px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.empty-action {
  margin-top: 20px;
}
</style>
