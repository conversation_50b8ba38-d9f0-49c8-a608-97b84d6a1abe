/**
 * 健康指标相关API接口
 */

import { request } from '@/utils/request'
import type {
  HealthRecord,
  HealthQuery,
  HealthStats,
  HealthTrend,
  HealthAlert,
  HealthReport,
  HealthGoal,
  HealthIndicatorConfig,
  CreateHealthRecordParams,
  BatchImportParams,
  ImportResult,
  HealthIndicatorType
} from '@/types/health'
import type { ApiResponse, PaginationResult } from '@/types/api'

/**
 * 获取健康记录列表
 */
export const getHealthRecords = (params: HealthQuery = {}) => {
  return request<PaginationResult<HealthRecord>>({
    url: '/health/records',
    method: 'GET',
    params
  })
}

/**
 * 获取健康记录详情
 */
export const getHealthRecord = (id: number) => {
  return request<HealthRecord>({
    url: `/health/records/${id}`,
    method: 'GET'
  })
}

/**
 * 创建健康记录
 */
export const createHealthRecord = (data: CreateHealthRecordParams) => {
  return request<HealthRecord>({
    url: '/health/records',
    method: 'POST',
    data
  })
}

/**
 * 批量创建健康记录
 */
export const batchCreateHealthRecords = (records: CreateHealthRecordParams[]) => {
  return request<HealthRecord[]>({
    url: '/health/records/batch',
    method: 'POST',
    data: { records }
  })
}

/**
 * 更新健康记录
 */
export const updateHealthRecord = (id: number, data: Partial<CreateHealthRecordParams>) => {
  return request<HealthRecord>({
    url: `/health/records/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除健康记录
 */
export const deleteHealthRecord = (id: number) => {
  return request<void>({
    url: `/health/records/${id}`,
    method: 'DELETE'
  })
}

/**
 * 批量删除健康记录
 */
export const batchDeleteHealthRecords = (ids: number[]) => {
  return request<void>({
    url: '/health/records/batch-delete',
    method: 'DELETE',
    data: { ids }
  })
}

/**
 * 获取健康统计信息
 */
export const getHealthStats = () => {
  return request<HealthStats>({
    url: '/health/stats',
    method: 'GET'
  })
}

/**
 * 获取会员健康统计
 */
export const getMemberHealthStats = (memberId: number) => {
  return request<HealthStats>({
    url: `/health/stats/member/${memberId}`,
    method: 'GET'
  })
}

/**
 * 获取健康趋势数据
 */
export const getHealthTrend = (memberId: number, indicatorType: HealthIndicatorType, days: number = 30) => {
  return request<HealthTrend>({
    url: '/health/trend',
    method: 'GET',
    params: { memberId, indicatorType, days }
  })
}

/**
 * 获取多个指标的趋势数据
 */
export const getMultipleHealthTrends = (memberId: number, indicatorTypes: HealthIndicatorType[], days: number = 30) => {
  return request<HealthTrend[]>({
    url: '/health/trends/multiple',
    method: 'GET',
    params: { memberId, indicatorTypes: indicatorTypes.join(','), days }
  })
}

/**
 * 获取异常预警列表
 */
export const getHealthAlerts = (processed?: boolean) => {
  return request<HealthAlert[]>({
    url: '/health/alerts',
    method: 'GET',
    params: { processed }
  })
}

/**
 * 处理异常预警
 */
export const processHealthAlert = (id: number, suggestions?: string[]) => {
  return request<HealthAlert>({
    url: `/health/alerts/${id}/process`,
    method: 'POST',
    data: { suggestions }
  })
}

/**
 * 获取健康指标配置
 */
export const getHealthIndicatorConfigs = () => {
  return request<HealthIndicatorConfig[]>({
    url: '/health/configs',
    method: 'GET'
  })
}

/**
 * 获取单个指标配置
 */
export const getHealthIndicatorConfig = (type: HealthIndicatorType) => {
  return request<HealthIndicatorConfig>({
    url: `/health/configs/${type}`,
    method: 'GET'
  })
}

/**
 * 更新健康指标配置
 */
export const updateHealthIndicatorConfig = (type: HealthIndicatorType, config: Partial<HealthIndicatorConfig>) => {
  return request<HealthIndicatorConfig>({
    url: `/health/configs/${type}`,
    method: 'PUT',
    data: config
  })
}

/**
 * 批量导入健康数据
 */
export const importHealthData = (params: BatchImportParams) => {
  const formData = new FormData()
  formData.append('file', params.file)
  if (params.memberId) {
    formData.append('memberId', String(params.memberId))
  }
  formData.append('skipErrors', String(params.skipErrors))
  
  return request<ImportResult>({
    url: '/health/import',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 下载导入模板
 */
export const downloadImportTemplate = () => {
  return request<Blob>({
    url: '/health/import/template',
    method: 'GET',
    responseType: 'blob'
  })
}

/**
 * 导出健康数据
 */
export const exportHealthData = (params: HealthQuery = {}) => {
  return request<Blob>({
    url: '/health/export',
    method: 'GET',
    params,
    responseType: 'blob'
  })
}

/**
 * 生成健康报告
 */
export const generateHealthReport = (memberId: number, period: string = 'month') => {
  return request<HealthReport>({
    url: '/health/report',
    method: 'POST',
    data: { memberId, period }
  })
}

/**
 * 获取健康目标列表
 */
export const getHealthGoals = (memberId?: number) => {
  return request<HealthGoal[]>({
    url: '/health/goals',
    method: 'GET',
    params: { memberId }
  })
}

/**
 * 创建健康目标
 */
export const createHealthGoal = (data: Omit<HealthGoal, 'id' | 'progress' | 'createdAt' | 'updatedAt'>) => {
  return request<HealthGoal>({
    url: '/health/goals',
    method: 'POST',
    data
  })
}

/**
 * 更新健康目标
 */
export const updateHealthGoal = (id: number, data: Partial<HealthGoal>) => {
  return request<HealthGoal>({
    url: `/health/goals/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除健康目标
 */
export const deleteHealthGoal = (id: number) => {
  return request<void>({
    url: `/health/goals/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取异常记录
 */
export const getAbnormalRecords = (params: HealthQuery = {}) => {
  return request<PaginationResult<HealthRecord>>({
    url: '/health/abnormal',
    method: 'GET',
    params: { ...params, isAbnormal: true }
  })
}

/**
 * 获取最新健康记录
 */
export const getLatestHealthRecords = (memberId: number) => {
  return request<HealthRecord[]>({
    url: `/health/latest/${memberId}`,
    method: 'GET'
  })
}

/**
 * 检查健康指标异常
 */
export const checkHealthAbnormal = (indicatorType: HealthIndicatorType, value: number | string) => {
  return request<{
    isAbnormal: boolean
    abnormalLevel: number
    message: string
    suggestions: string[]
  }>({
    url: '/health/check-abnormal',
    method: 'POST',
    data: { indicatorType, value }
  })
}
