/**
 * 表格通用逻辑组合式函数
 */

import { ref, reactive, computed } from 'vue'
import type { Ref } from 'vue'

export interface TableConfig {
  page?: number
  limit?: number
  total?: number
}

export interface UseTableReturn<T = any> {
  loading: Ref<boolean>
  tableData: Ref<T[]>
  selectedRows: Ref<T[]>
  pagination: {
    page: number
    limit: number
    total: number
  }
  handleSelectionChange: (selection: T[]) => void
  handleSizeChange: (size: number) => void
  handleCurrentChange: (page: number) => void
  refresh: () => void
  resetPagination: () => void
}

export function useTable<T = any>(
  fetchData: (params: any) => Promise<{ data: { list: T[]; total: number } }>,
  config: TableConfig = {}
): UseTableReturn<T> {
  const loading = ref(false)
  const tableData = ref<T[]>([])
  const selectedRows = ref<T[]>([])
  
  const pagination = reactive({
    page: config.page || 1,
    limit: config.limit || 20,
    total: config.total || 0
  })

  // 获取数据
  const loadData = async (params: any = {}) => {
    loading.value = true
    try {
      const response = await fetchData({
        page: pagination.page,
        limit: pagination.limit,
        ...params
      })
      tableData.value = response.data.list
      pagination.total = response.data.total
    } catch (error) {
      console.error('Load table data error:', error)
      tableData.value = []
      pagination.total = 0
    } finally {
      loading.value = false
    }
  }

  // 选择变化
  const handleSelectionChange = (selection: T[]) => {
    selectedRows.value = selection
  }

  // 每页大小变化
  const handleSizeChange = (size: number) => {
    pagination.limit = size
    pagination.page = 1
    loadData()
  }

  // 当前页变化
  const handleCurrentChange = (page: number) => {
    pagination.page = page
    loadData()
  }

  // 刷新
  const refresh = () => {
    loadData()
  }

  // 重置分页
  const resetPagination = () => {
    pagination.page = 1
    pagination.total = 0
    selectedRows.value = []
  }

  return {
    loading,
    tableData,
    selectedRows,
    pagination,
    handleSelectionChange,
    handleSizeChange,
    handleCurrentChange,
    refresh,
    resetPagination
  }
}
