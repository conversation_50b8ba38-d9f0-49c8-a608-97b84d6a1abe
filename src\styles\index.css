/* 全局样式文件 */

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* CSS变量定义 - 医疗健康主题 */
:root {
  /* 主色调 */
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  /* 医疗主题色 */
  --medical-blue: #1890ff;
  --medical-green: #52c41a;
  --medical-teal: #13c2c2;
  --medical-purple: #722ed1;
  --medical-orange: #fa8c16;
  
  /* 背景色 */
  --bg-color: #f5f7fa;
  --bg-color-light: #fafbfc;
  --bg-color-dark: #f0f2f5;
  
  /* 边框色 */
  --border-color: #dcdfe6;
  --border-color-light: #e4e7ed;
  --border-color-dark: #d4d7de;
  
  /* 文字色 */
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --text-color-placeholder: #c0c4cc;
  
  /* 阴影 */
  --box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  
  /* 圆角 */
  --border-radius-small: 2px;
  --border-radius-base: 4px;
  --border-radius-large: 6px;
  --border-radius-round: 20px;
  --border-radius-circle: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* 医疗主题渐变背景 */
.medical-gradient {
  background: linear-gradient(135deg, var(--medical-blue) 0%, var(--medical-teal) 100%);
}

.medical-gradient-light {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(19, 194, 194, 0.1) 100%);
}

/* 卡片样式增强 */
.el-card {
  border-radius: var(--border-radius-large);
  border: 1px solid var(--border-color-light);
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: var(--box-shadow-dark);
}

/* 按钮样式增强 */
.el-button {
  border-radius: var(--border-radius-base);
  font-weight: 500;
  transition: all 0.3s ease;
}

.el-button--primary {
  background-color: var(--medical-blue);
  border-color: var(--medical-blue);
}

.el-button--primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.el-button--success {
  background-color: var(--medical-green);
  border-color: var(--medical-green);
}

.el-button--success:hover {
  background-color: #73d13d;
  border-color: #73d13d;
}

/* 表格样式增强 */
.el-table {
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.el-table th {
  background-color: var(--bg-color-light);
  color: var(--text-color-primary);
  font-weight: 600;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafbfc;
}

/* 表单样式增强 */
.el-form-item__label {
  color: var(--text-color-primary);
  font-weight: 500;
}

.el-input__inner {
  border-radius: var(--border-radius-base);
}

.el-input__inner:focus {
  border-color: var(--medical-blue);
}

/* 标签样式增强 */
.el-tag {
  border-radius: var(--border-radius-small);
  font-weight: 500;
}

.el-tag--primary {
  background-color: rgba(24, 144, 255, 0.1);
  border-color: var(--medical-blue);
  color: var(--medical-blue);
}

.el-tag--success {
  background-color: rgba(82, 196, 26, 0.1);
  border-color: var(--medical-green);
  color: var(--medical-green);
}

/* 分页样式增强 */
.el-pagination {
  margin-top: var(--spacing-xl);
}

.el-pagination .el-pager li.active {
  background-color: var(--medical-blue);
  color: white;
}

/* 对话框样式增强 */
.el-dialog {
  border-radius: var(--border-radius-large);
}

.el-dialog__header {
  border-bottom: 1px solid var(--border-color-light);
  padding-bottom: var(--spacing-lg);
}

/* 消息提示样式增强 */
.el-message {
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
}

/* 加载样式增强 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 自定义组件样式 */
.health-card {
  background: white;
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
}

.health-card:hover {
  box-shadow: var(--box-shadow-dark);
  transform: translateY(-2px);
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: var(--border-radius-circle);
  margin-right: var(--spacing-sm);
}

.status-dot--success {
  background-color: var(--medical-green);
}

.status-dot--warning {
  background-color: var(--warning-color);
}

.status-dot--danger {
  background-color: var(--danger-color);
}

.status-dot--info {
  background-color: var(--info-color);
}
