<template>
  <el-dialog
    v-model="visible"
    title="位置信息"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="location-content">
      <!-- 当前位置信息 -->
      <div class="current-location">
        <h4>当前位置</h4>
        <div v-if="currentLocation" class="location-info">
          <div class="location-item">
            <span class="label">地址：</span>
            <span class="value">{{ currentLocation.address }}</span>
          </div>
          <div class="location-item">
            <span class="label">经纬度：</span>
            <span class="value">{{ currentLocation.latitude }}, {{ currentLocation.longitude }}</span>
          </div>
          <div class="location-item">
            <span class="label">定位精度：</span>
            <span class="value">{{ currentLocation.accuracy }}米</span>
          </div>
          <div class="location-item">
            <span class="label">定位时间：</span>
            <span class="value">{{ formatDate(currentLocation.timestamp) }}</span>
          </div>
        </div>
        <div v-else class="no-location">
          <el-empty description="暂无位置信息" :image-size="80" />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="location-actions">
        <el-button 
          type="primary" 
          :loading="locationLoading" 
          @click="getCurrentLocation"
        >
          <el-icon><Location /></el-icon>
          获取当前位置
        </el-button>
        <el-button 
          v-if="currentLocation"
          type="success" 
          @click="openMap"
        >
          <el-icon><MapLocation /></el-icon>
          在地图中查看
        </el-button>
        <el-button 
          v-if="currentLocation"
          type="info" 
          @click="copyLocation"
        >
          <el-icon><CopyDocument /></el-icon>
          复制位置信息
        </el-button>
      </div>

      <!-- 地图容器 -->
      <div v-if="showMap" class="map-container">
        <h4>位置地图</h4>
        <div ref="mapRef" class="map" style="height: 300px; width: 100%;" />
      </div>

      <!-- 位置历史 -->
      <div v-if="locationHistory.length > 0" class="location-history">
        <h4>最近位置记录</h4>
        <el-table :data="locationHistory" size="small" max-height="200">
          <el-table-column prop="address" label="地址" show-overflow-tooltip />
          <el-table-column prop="accuracy" label="精度" width="80">
            <template #default="{ row }">
              {{ row.accuracy }}m
            </template>
          </el-table-column>
          <el-table-column prop="timestamp" label="时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.timestamp, 'MM-DD HH:mm') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="useHistoryLocation(row)">
                使用
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 手动输入位置 -->
      <div class="manual-location">
        <h4>手动输入位置</h4>
        <el-form :model="manualForm" label-width="80px">
          <el-form-item label="地址">
            <el-input
              v-model="manualForm.address"
              placeholder="请输入详细地址"
              @blur="geocodeAddress"
            />
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="纬度">
                <el-input-number
                  v-model="manualForm.latitude"
                  :precision="6"
                  :min="-90"
                  :max="90"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="经度">
                <el-input-number
                  v-model="manualForm.longitude"
                  :precision="6"
                  :min="-180"
                  :max="180"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="useManualLocation">
              使用此位置
            </el-button>
            <el-button @click="resetManualForm">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="currentLocation" 
          type="primary" 
          @click="confirmLocation"
        >
          确认使用
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Location, MapLocation, CopyDocument } from '@element-plus/icons-vue'
import { useVisitStore } from '@/stores/visit'
import { formatDate } from '@/utils'
import type { LocationInfo } from '@/types/visit'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', location: LocationInfo): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visitStore = useVisitStore()
const locationLoading = ref(false)
const showMap = ref(false)
const mapRef = ref<HTMLElement>()
const currentLocation = ref<LocationInfo | null>(null)
const locationHistory = ref<LocationInfo[]>([])

// 手动输入表单
const manualForm = reactive({
  address: '',
  latitude: null as number | null,
  longitude: null as number | null
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取当前位置
const getCurrentLocation = async () => {
  try {
    locationLoading.value = true
    const location = await visitStore.getCurrentLocationAction()
    currentLocation.value = location
    
    // 添加到历史记录
    addToHistory(location)
    
    // 显示地图
    showMap.value = true
    nextTick(() => {
      initMap()
    })
    
    ElMessage.success('位置获取成功')
  } catch (error) {
    console.error('获取位置失败:', error)
    ElMessage.error('获取位置失败，请检查定位权限')
  } finally {
    locationLoading.value = false
  }
}

// 在地图中查看
const openMap = () => {
  if (!currentLocation.value) return
  
  const { latitude, longitude } = currentLocation.value
  const url = `https://maps.google.com/maps?q=${latitude},${longitude}`
  window.open(url, '_blank')
}

// 复制位置信息
const copyLocation = async () => {
  if (!currentLocation.value) return
  
  const locationText = `地址: ${currentLocation.value.address}\n经纬度: ${currentLocation.value.latitude}, ${currentLocation.value.longitude}`
  
  try {
    await navigator.clipboard.writeText(locationText)
    ElMessage.success('位置信息已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = locationText
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('位置信息已复制到剪贴板')
  }
}

// 初始化地图
const initMap = () => {
  if (!mapRef.value || !currentLocation.value) return
  
  // 这里可以集成第三方地图服务，如高德地图、百度地图等
  // 由于需要API密钥，这里只是示例代码
  mapRef.value.innerHTML = `
    <div style="
      height: 100%; 
      background: #f0f0f0; 
      display: flex; 
      align-items: center; 
      justify-content: center;
      border: 1px solid #ddd;
      border-radius: 4px;
    ">
      <div style="text-align: center; color: #666;">
        <p>地图显示区域</p>
        <p>位置: ${currentLocation.value.address}</p>
        <p>坐标: ${currentLocation.value.latitude}, ${currentLocation.value.longitude}</p>
      </div>
    </div>
  `
}

// 地理编码 - 地址转坐标
const geocodeAddress = async () => {
  if (!manualForm.address) return
  
  try {
    // 这里应该调用地理编码API
    // 由于需要API密钥，这里只是示例
    ElMessage.info('地理编码功能需要配置地图服务API')
  } catch (error) {
    console.error('地理编码失败:', error)
  }
}

// 使用手动输入的位置
const useManualLocation = () => {
  if (!manualForm.address || !manualForm.latitude || !manualForm.longitude) {
    ElMessage.warning('请完整填写位置信息')
    return
  }
  
  const location: LocationInfo = {
    latitude: manualForm.latitude,
    longitude: manualForm.longitude,
    address: manualForm.address,
    timestamp: new Date().toISOString()
  }
  
  currentLocation.value = location
  addToHistory(location)
  
  ElMessage.success('位置设置成功')
}

// 重置手动输入表单
const resetManualForm = () => {
  Object.assign(manualForm, {
    address: '',
    latitude: null,
    longitude: null
  })
}

// 使用历史位置
const useHistoryLocation = (location: LocationInfo) => {
  currentLocation.value = { ...location }
  ElMessage.success('已选择历史位置')
}

// 添加到历史记录
const addToHistory = (location: LocationInfo) => {
  // 避免重复添加相同位置
  const exists = locationHistory.value.some(item => 
    Math.abs(item.latitude - location.latitude) < 0.0001 &&
    Math.abs(item.longitude - location.longitude) < 0.0001
  )
  
  if (!exists) {
    locationHistory.value.unshift(location)
    // 只保留最近10条记录
    if (locationHistory.value.length > 10) {
      locationHistory.value = locationHistory.value.slice(0, 10)
    }
    
    // 保存到本地存储
    localStorage.setItem('visit_location_history', JSON.stringify(locationHistory.value))
  }
}

// 确认使用位置
const confirmLocation = () => {
  if (currentLocation.value) {
    emit('confirm', currentLocation.value)
    handleClose()
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 加载历史记录
const loadLocationHistory = () => {
  try {
    const history = localStorage.getItem('visit_location_history')
    if (history) {
      locationHistory.value = JSON.parse(history)
    }
  } catch (error) {
    console.error('加载位置历史失败:', error)
  }
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    loadLocationHistory()
  }
})
</script>

<style scoped>
.location-content {
  max-height: 600px;
  overflow-y: auto;
}

.current-location,
.location-history,
.manual-location {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.current-location:last-child,
.location-history:last-child,
.manual-location:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.location-info {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.location-item {
  display: flex;
  margin-bottom: 8px;
}

.location-item:last-child {
  margin-bottom: 0;
}

.label {
  min-width: 80px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.value {
  color: var(--el-text-color-primary);
  font-size: 14px;
  word-break: break-all;
}

.no-location {
  text-align: center;
  padding: 40px 0;
}

.location-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.map-container {
  margin-bottom: 24px;
}

.map {
  border-radius: 6px;
  overflow: hidden;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>
