/**
 * 回访相关类型定义
 */

// 回访类型
export enum VisitType {
  REGULAR = 1,      // 定期回访
  EMERGENCY = 2,    // 紧急回访
  SPECIAL = 3,      // 专项回访
  FOLLOW_UP = 4,    // 随访回访
  SATISFACTION = 5  // 满意度回访
}

// 回访状态
export enum VisitStatus {
  PLANNED = 1,      // 已计划
  IN_PROGRESS = 2,  // 进行中
  COMPLETED = 3,    // 已完成
  CANCELLED = 4,    // 已取消
  OVERDUE = 5       // 已逾期
}

// GPS位置信息
export interface LocationInfo {
  latitude: number          // 纬度
  longitude: number         // 经度
  address: string          // 详细地址
  accuracy?: number        // 定位精度(米)
  timestamp: string        // 定位时间
}

// 多媒体文件信息
export interface MediaFile {
  id: string
  type: 'image' | 'video' | 'audio'  // 文件类型
  url: string                        // 文件URL
  thumbnail?: string                 // 缩略图URL
  description?: string               // 文件描述
  size: number                       // 文件大小(字节)
  duration?: number                  // 时长(秒，仅音视频)
  createdAt: string
}

// 回访记录
export interface Visit {
  id: number
  memberId: number                   // 会员ID
  memberName: string                 // 会员姓名
  butlerId: number                  // 管家ID
  butlerName: string                // 管家姓名
  visitType: VisitType              // 回访类型
  status: VisitStatus               // 回访状态
  scheduledAt: string               // 计划回访时间
  actualStartAt?: string            // 实际开始时间
  actualEndAt?: string              // 实际结束时间
  location?: LocationInfo           // 回访位置
  
  // 回访内容
  healthAssessment: string          // 健康状况评估
  serviceExecution: string          // 服务执行情况
  problemsFound: string             // 发现的问题
  suggestions: string               // 处理建议
  nextVisitPlan: string            // 下次回访计划
  satisfactionScore: number        // 满意度评分(1-5)
  
  // 多媒体记录
  images?: MediaFile[]              // 现场照片
  videos?: MediaFile[]              // 视频记录
  audios?: MediaFile[]              // 语音记录
  
  // 其他信息
  notes?: string                    // 备注
  tags?: string[]                   // 标签
  duration?: number                 // 回访时长(分钟)
  
  createdAt: string
  updatedAt: string
}

// 回访计划
export interface VisitPlan {
  id: number
  memberId: number                  // 会员ID
  memberName: string                // 会员姓名
  visitType: VisitType             // 回访类型
  frequency: string                // 回访频率(weekly/monthly/quarterly)
  nextVisitDate: string            // 下次回访日期
  reminderDays: number             // 提前提醒天数
  isActive: boolean                // 是否启用
  lastVisitAt?: string             // 上次回访时间
  createdAt: string
  updatedAt: string
}

// 回访查询参数
export interface VisitQuery {
  page?: number
  limit?: number
  memberId?: number
  memberName?: string
  butlerId?: number
  visitType?: VisitType
  status?: VisitStatus
  startDate?: string
  endDate?: string
  keyword?: string
}

// 回访统计信息
export interface VisitStats {
  total: number                     // 总回访数
  planned: number                   // 已计划
  inProgress: number               // 进行中
  completed: number                // 已完成
  overdue: number                  // 已逾期
  todayVisits: number              // 今日回访
  weeklyVisits: number             // 本周回访
  monthlyVisits: number            // 本月回访
  avgDuration: number              // 平均时长(分钟)
  avgSatisfaction: number          // 平均满意度
  completionRate: number           // 完成率
}

// 创建回访记录参数
export interface CreateVisitParams {
  memberId: number
  visitType: VisitType
  scheduledAt: string
  location?: Omit<LocationInfo, 'timestamp'>
  healthAssessment: string
  serviceExecution: string
  problemsFound: string
  suggestions: string
  nextVisitPlan: string
  satisfactionScore: number
  images?: File[]
  videos?: File[]
  audios?: File[]
  notes?: string
  tags?: string[]
}

// 更新回访记录参数
export interface UpdateVisitParams {
  visitType?: VisitType
  status?: VisitStatus
  scheduledAt?: string
  actualStartAt?: string
  actualEndAt?: string
  location?: LocationInfo
  healthAssessment?: string
  serviceExecution?: string
  problemsFound?: string
  suggestions?: string
  nextVisitPlan?: string
  satisfactionScore?: number
  notes?: string
  tags?: string[]
}

// 回访提醒信息
export interface VisitReminder {
  id: number
  visitId?: number                  // 关联的回访ID
  memberId: number                  // 会员ID
  memberName: string                // 会员姓名
  visitType: VisitType             // 回访类型
  scheduledAt: string              // 计划时间
  reminderType: 'advance' | 'due' | 'overdue'  // 提醒类型
  isProcessed: boolean             // 是否已处理
  createdAt: string
}

// 回访模板
export interface VisitTemplate {
  id: number
  name: string                      // 模板名称
  visitType: VisitType             // 回访类型
  description: string              // 模板描述
  healthAssessmentTemplate: string // 健康评估模板
  serviceExecutionTemplate: string // 服务执行模板
  problemsFoundTemplate: string    // 问题发现模板
  suggestionsTemplate: string      // 建议模板
  nextVisitPlanTemplate: string    // 下次计划模板
  isDefault: boolean               // 是否默认模板
  createdAt: string
  updatedAt: string
}
