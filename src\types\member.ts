// 会员相关类型定义

export interface Member {
  id: number
  memberNo: string // 会员编号
  name: string
  gender: number // 1-男 2-女
  age: number
  phone: string
  chiefComplaint: string // 主诉
  avatar?: string
  idCard?: string
  emergencyContact?: string
  emergencyPhone?: string
  address?: string
  butlerId: number
  status: number // 1-活跃 2-暂停 3-注销
  healthTags: string[] // 健康标签
  createdAt: string
  updatedAt: string
}

export interface MemberForm {
  name: string
  gender: number
  age: number
  phone: string
  chiefComplaint: string
  avatar?: string
  idCard?: string
  emergencyContact?: string
  emergencyPhone?: string
  address?: string
  healthTags?: string[]
}

export interface MemberMedia {
  id: number
  memberId: number
  fileType: number // 1-图片 2-视频 3-文档
  fileUrl: string
  fileName: string
  fileSize: number
  description?: string
  createdAt: string
}

export interface HealthIndicator {
  id: number
  memberId: number
  indicatorType: string // blood_pressure, blood_glucose, etc.
  value: string
  unit: string
  measuredAt: string
  notes?: string
  isAbnormal: boolean
  createdAt: string
}

export interface VisitRecord {
  id: number
  memberId: number
  butlerId: number
  visitType: number // 1-定期回访 2-紧急回访 3-专项回访
  scheduledAt: string
  completedAt?: string
  location: {
    latitude: number
    longitude: number
    address: string
  }
  description: string
  photos: string[]
  satisfactionScore?: number // 1-5分
  status: number // 1-待回访 2-进行中 3-已完成 4-已取消
  createdAt: string
}

export interface MemberSearchParams {
  keyword?: string
  gender?: number
  ageRange?: [number, number]
  status?: number
  healthTags?: string[]
  butlerId?: number
  page: number
  limit: number
}
