<template>
  <div class="questionnaire-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">📋 问卷管理</h1>
        <p class="page-desc">管理和推送健康问卷，收集会员健康信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="createQuestionnaire">
          创建问卷
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon questionnaires">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalQuestionnaires }}</div>
                <div class="stat-label">问卷总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon sent">
                <el-icon><Promotion /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.sentToday }}</div>
                <div class="stat-label">今日推送</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completedToday }}</div>
                <div class="stat-label">今日完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon rate">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completionRate }}%</div>
                <div class="stat-label">完成率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card shadow="never">
        <el-form :model="searchForm" inline>
          <el-form-item label="问卷名称">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索问卷名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          
          <el-form-item label="问卷类型">
            <el-select v-model="searchForm.type" placeholder="全部类型" clearable style="width: 150px">
              <el-option label="健康评估" value="health_assessment" />
              <el-option label="症状记录" value="symptom_record" />
              <el-option label="用药反馈" value="medication_feedback" />
              <el-option label="生活习惯" value="lifestyle" />
              <el-option label="满意度调查" value="satisfaction" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="全部状态" clearable style="width: 120px">
              <el-option label="草稿" :value="0" />
              <el-option label="已发布" :value="1" />
              <el-option label="已停用" :value="2" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 问卷列表 -->
    <div class="table-section">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span>问卷列表 ({{ total }})</span>
            <div class="header-actions">
              <el-button size="small" :icon="Download" @click="exportData">
                导出数据
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          v-loading="loading"
          :data="questionnaireList"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="问卷信息" min-width="250">
            <template #default="{ row }">
              <div class="questionnaire-info">
                <div class="questionnaire-title">{{ row.title }}</div>
                <div class="questionnaire-desc">{{ row.description }}</div>
                <div class="questionnaire-meta">
                  <el-tag size="small" :type="getTypeTagType(row.type)">
                    {{ getTypeText(row.type) }}
                  </el-tag>
                  <span class="question-count">{{ row.questionCount }} 题</span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="推送统计" width="120">
            <template #default="{ row }">
              <div class="push-stats">
                <div>推送：{{ row.pushCount }}</div>
                <div>完成：{{ row.completedCount }}</div>
                <div class="completion-rate">
                  完成率：{{ calculateCompletionRate(row) }}%
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag
                :type="getStatusTagType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" type="text" @click="viewQuestionnaire(row.id)">
                查看
              </el-button>
              <el-button size="small" type="text" @click="editQuestionnaire(row.id)">
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="text" 
                :disabled="row.status !== 1"
                @click="pushQuestionnaire(row)"
              >
                推送
              </el-button>
              <el-dropdown @command="(command) => handleCommand(command, row)">
                <el-button size="small" type="text">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="copy">复制问卷</el-dropdown-item>
                    <el-dropdown-item command="statistics">查看统计</el-dropdown-item>
                    <el-dropdown-item command="responses">查看回答</el-dropdown-item>
                    <el-dropdown-item divided command="delete" class="danger">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 推送问卷对话框 -->
    <el-dialog
      v-model="pushDialogVisible"
      title="推送问卷"
      width="600px"
    >
      <div class="push-placeholder">
        <el-icon size="48" color="#ccc"><Promotion /></el-icon>
        <p>问卷推送功能开发中...</p>
        <div class="push-actions">
          <el-button @click="pushDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePushSuccess">确认推送</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Download,
  ArrowDown,
  Document,
  Promotion,
  CircleCheck,
  TrendCharts
} from '@element-plus/icons-vue'
import QuestionnairePush from '@/components/QuestionnairePush.vue'
import dayjs from 'dayjs'

const router = useRouter()

const loading = ref(false)
const pushDialogVisible = ref(false)
const selectedQuestionnaire = ref(null)
const selectedQuestionnaires = ref([])
const total = ref(0)

const searchForm = reactive({
  keyword: '',
  type: '',
  status: undefined as number | undefined
})

const pagination = reactive({
  page: 1,
  limit: 20
})

// 模拟数据
const questionnaireList = ref([
  {
    id: 1,
    title: '高血压患者健康评估问卷',
    description: '针对高血压患者的综合健康状况评估',
    type: 'health_assessment',
    questionCount: 15,
    pushCount: 45,
    completedCount: 38,
    status: 1,
    createdAt: '2024-01-20T10:00:00Z'
  },
  {
    id: 2,
    title: '用药依从性调查',
    description: '了解患者用药情况和依从性',
    type: 'medication_feedback',
    questionCount: 8,
    pushCount: 32,
    completedCount: 29,
    status: 1,
    createdAt: '2024-01-18T14:30:00Z'
  },
  {
    id: 3,
    title: '生活习惯调查问卷',
    description: '收集患者日常生活习惯信息',
    type: 'lifestyle',
    questionCount: 12,
    pushCount: 0,
    completedCount: 0,
    status: 0,
    createdAt: '2024-01-25T09:15:00Z'
  }
])

const stats = computed(() => ({
  totalQuestionnaires: questionnaireList.value.length,
  sentToday: 8,
  completedToday: 6,
  completionRate: 85
}))

// 获取问卷列表
const fetchQuestionnaireList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    total.value = questionnaireList.value.length
  } catch (error) {
    ElMessage.error('获取问卷列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchQuestionnaireList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    type: '',
    status: undefined
  })
  pagination.page = 1
  fetchQuestionnaireList()
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchQuestionnaireList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchQuestionnaireList()
}

// 选择
const handleSelectionChange = (selection: any[]) => {
  selectedQuestionnaires.value = selection
}

// 计算完成率
const calculateCompletionRate = (questionnaire: any) => {
  if (questionnaire.pushCount === 0) return 0
  return Math.round((questionnaire.completedCount / questionnaire.pushCount) * 100)
}

// 状态相关
const getStatusTagType = (status: number) => {
  const types = { 0: 'info', 1: 'success', 2: 'danger' }
  return types[status as keyof typeof types] || 'info'
}

const getStatusText = (status: number) => {
  const texts = { 0: '草稿', 1: '已发布', 2: '已停用' }
  return texts[status as keyof typeof texts] || '未知'
}

const getTypeTagType = (type: string) => {
  const types = {
    health_assessment: 'primary',
    symptom_record: 'warning',
    medication_feedback: 'success',
    lifestyle: 'info',
    satisfaction: 'danger'
  }
  return types[type as keyof typeof types] || 'info'
}

const getTypeText = (type: string) => {
  const texts = {
    health_assessment: '健康评估',
    symptom_record: '症状记录',
    medication_feedback: '用药反馈',
    lifestyle: '生活习惯',
    satisfaction: '满意度调查'
  }
  return texts[type as keyof typeof texts] || '其他'
}

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

// 操作方法
const createQuestionnaire = () => {
  router.push('/questionnaires/create')
}

const viewQuestionnaire = (id: number) => {
  router.push(`/questionnaires/${id}`)
}

const editQuestionnaire = (id: number) => {
  router.push(`/questionnaires/${id}/edit`)
}

const pushQuestionnaire = (questionnaire: any) => {
  selectedQuestionnaire.value = questionnaire
  pushDialogVisible.value = true
}

const handleCommand = async (command: string, questionnaire: any) => {
  switch (command) {
    case 'copy':
      ElMessage.info('复制功能开发中...')
      break
    case 'statistics':
      router.push(`/questionnaires/${questionnaire.id}/statistics`)
      break
    case 'responses':
      router.push(`/questionnaires/${questionnaire.id}/responses`)
      break
    case 'delete':
      await handleDelete(questionnaire)
      break
  }
}

const handleDelete = async (questionnaire: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除问卷 "${questionnaire.title}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = questionnaireList.value.findIndex(q => q.id === questionnaire.id)
    if (index > -1) {
      questionnaireList.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handlePushSuccess = () => {
  ElMessage.success('问卷推送成功')
  fetchQuestionnaireList()
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

onMounted(() => {
  fetchQuestionnaireList()
})
</script>

<style scoped>
.questionnaire-list-container {
  padding: 20px;
  background: var(--bg-color);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.page-desc {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
  border-radius: 12px;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.questionnaires { background: var(--medical-blue); }
.stat-icon.sent { background: var(--medical-teal); }
.stat-icon.completed { background: var(--medical-green); }
.stat-icon.rate { background: var(--warning-color); }

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.filter-section {
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.questionnaire-info {
  padding: 8px 0;
}

.questionnaire-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  font-size: 15px;
}

.questionnaire-desc {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.questionnaire-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.question-count {
  font-size: 12px;
  color: #999;
}

.push-stats {
  font-size: 13px;
  line-height: 1.6;
}

.push-stats > div {
  margin-bottom: 2px;
}

.completion-rate {
  color: var(--medical-green);
  font-weight: 500;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.danger {
  color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .questionnaire-list-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}
</style>
