/**
 * 权限管理组合式函数
 */

import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { MENU_PERMISSIONS } from '@/constants'

export function usePermission() {
  const userStore = useUserStore()

  // 检查是否有指定权限
  const hasPermission = (permission: string | string[]): boolean => {
    const userPermissions = userStore.permissions || []
    
    if (Array.isArray(permission)) {
      return permission.some(p => userPermissions.includes(p))
    }
    
    return userPermissions.includes(permission)
  }

  // 检查是否有指定角色
  const hasRole = (role: string | string[]): boolean => {
    const userRole = userStore.userInfo?.role
    
    if (!userRole) return false
    
    if (Array.isArray(role)) {
      return role.includes(userRole)
    }
    
    return userRole === role
  }

  // 检查是否为管理员
  const isAdmin = computed(() => {
    return userStore.userInfo?.role === 'admin'
  })

  // 检查是否为管家
  const isButler = computed(() => {
    return userStore.userInfo?.role === 'butler'
  })

  // 检查是否为医生
  const isDoctor = computed(() => {
    return userStore.userInfo?.role === 'doctor'
  })

  // 菜单权限检查
  const canViewDashboard = computed(() => {
    return hasPermission(MENU_PERMISSIONS.DASHBOARD)
  })

  const canManageMember = computed(() => {
    return hasPermission(MENU_PERMISSIONS.MEMBER_MANAGE)
  })

  const canCreateMember = computed(() => {
    return hasPermission(MENU_PERMISSIONS.MEMBER_CREATE)
  })

  const canEditMember = computed(() => {
    return hasPermission(MENU_PERMISSIONS.MEMBER_EDIT)
  })

  const canDeleteMember = computed(() => {
    return hasPermission(MENU_PERMISSIONS.MEMBER_DELETE)
  })

  const canManageReminder = computed(() => {
    return hasPermission(MENU_PERMISSIONS.REMINDER_MANAGE)
  })

  const canManageQuestionnaire = computed(() => {
    return hasPermission(MENU_PERMISSIONS.QUESTIONNAIRE_MANAGE)
  })

  const canViewStatistics = computed(() => {
    return hasPermission(MENU_PERMISSIONS.STATISTICS_VIEW)
  })

  return {
    hasPermission,
    hasRole,
    isAdmin,
    isButler,
    isDoctor,
    canViewDashboard,
    canManageMember,
    canCreateMember,
    canEditMember,
    canDeleteMember,
    canManageReminder,
    canManageQuestionnaire,
    canViewStatistics
  }
}
