import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import App from './App.vue'
import router from './router'
import './styles/index.css'
import './utils/dayjs'
import { setupGlobalErrorHandler } from './utils/errorHandler'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 设置全局错误处理
setupGlobalErrorHandler()

// Vue错误处理
app.config.errorHandler = (error: unknown, _instance: unknown, info: string) => {
  console.error('Vue error:', error, info)
}

app.mount('#app')
