<template>
  <div class="purchase-records">
    <!-- 头部操作区 -->
    <div class="header-actions">
      <div class="header-left">
        <h3>购买记录</h3>
        <p>管理会员的购买历史和订单信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          新增订单
        </el-button>
        <el-button :icon="Refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="订单号">
            <el-input
              v-model="searchForm.orderNo"
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="会员姓名">
            <el-input
              v-model="searchForm.memberName"
              placeholder="请输入会员姓名"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="待支付" value="pending" />
              <el-option label="已支付" value="paid" />
              <el-option label="已发货" value="shipped" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
              <el-option label="已退款" value="refunded" />
            </el-select>
          </el-form-item>
          <el-form-item label="商品类型">
            <el-select v-model="searchForm.productType" placeholder="请选择类型" clearable>
              <el-option label="健康服务" value="service" />
              <el-option label="保健品" value="supplement" />
              <el-option label="医疗器械" value="device" />
              <el-option label="体检套餐" value="checkup" />
            </el-select>
          </el-form-item>
          <el-form-item label="购买时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total-icon">
                <el-icon><ShoppingBag /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats?.totalOrders || 0 }}</div>
                <div class="stat-label">总订单数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon amount-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">¥{{ formatAmount(stats?.totalAmount || 0) }}</div>
                <div class="stat-label">总金额</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats?.pendingOrders || 0 }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats?.completedOrders || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 订单列表 -->
    <div class="order-list">
      <el-card>
        <el-table :data="orders" v-loading="loading">
          <el-table-column prop="orderNo" label="订单号" width="180" />
          <el-table-column prop="memberName" label="会员姓名" width="120" />
          <el-table-column prop="productName" label="商品名称" min-width="200" />
          <el-table-column prop="productType" label="商品类型" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ getProductTypeText(row.productType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column prop="unitPrice" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ formatAmount(row.unitPrice) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="总金额" width="120">
            <template #default="{ row }">
              <span class="amount-text">¥{{ formatAmount(row.totalAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="购买时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewOrder(row)">
                查看
              </el-button>
              <el-button 
                v-if="row.status === 'pending'"
                type="text" 
                size="small" 
                @click="processPayment(row)"
              >
                处理支付
              </el-button>
              <el-button 
                v-if="row.status === 'paid'"
                type="text" 
                size="small" 
                @click="shipOrder(row)"
              >
                发货
              </el-button>
              <el-button 
                v-if="['pending', 'paid'].includes(row.status)"
                type="text" 
                size="small" 
                @click="cancelOrder(row)"
              >
                取消
              </el-button>
              <el-dropdown v-if="row.status === 'completed'">
                <el-button type="text" size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="generateReminder(row)">
                      生成提醒
                    </el-dropdown-item>
                    <el-dropdown-item @click="exportOrder(row)">
                      导出订单
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="订单详情" width="800px">
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions title="订单信息" :column="2" border>
          <el-descriptions-item label="订单号">
            {{ currentOrder.orderNo }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="会员姓名">
            {{ currentOrder.memberName }}
          </el-descriptions-item>
          <el-descriptions-item label="会员手机">
            {{ currentOrder.memberPhone }}
          </el-descriptions-item>
          <el-descriptions-item label="商品名称">
            {{ currentOrder.productName }}
          </el-descriptions-item>
          <el-descriptions-item label="商品类型">
            {{ getProductTypeText(currentOrder.productType) }}
          </el-descriptions-item>
          <el-descriptions-item label="数量">
            {{ currentOrder.quantity }}
          </el-descriptions-item>
          <el-descriptions-item label="单价">
            ¥{{ formatAmount(currentOrder.unitPrice) }}
          </el-descriptions-item>
          <el-descriptions-item label="总金额">
            <span class="amount-text">¥{{ formatAmount(currentOrder.totalAmount) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            {{ currentOrder.paymentMethod || '未支付' }}
          </el-descriptions-item>
          <el-descriptions-item label="购买时间">
            {{ formatDate(currentOrder.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">
            {{ currentOrder.paidAt ? formatDate(currentOrder.paidAt) : '未支付' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ currentOrder.notes || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 订单状态历史 -->
        <div class="order-history">
          <h4>订单状态历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="history in currentOrder.statusHistory"
              :key="history.id"
              :timestamp="formatDate(history.createdAt)"
              placement="top"
            >
              <div class="history-content">
                <div class="history-status">
                  状态变更为：{{ getStatusText(history.status) }}
                </div>
                <div class="history-operator">
                  操作人：{{ history.operatorName }}
                </div>
                <div v-if="history.notes" class="history-notes">
                  备注：{{ history.notes }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 新增订单对话框 -->
    <el-dialog v-model="showCreateDialog" title="新增订单" width="600px">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="会员" prop="memberId">
          <el-select
            v-model="createForm.memberId"
            placeholder="请选择会员"
            filterable
            remote
            :remote-method="searchMembers"
            style="width: 100%"
          >
            <el-option
              v-for="member in memberOptions"
              :key="member.id"
              :label="member.name"
              :value="member.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="createForm.productName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品类型" prop="productType">
          <el-select v-model="createForm.productType" placeholder="请选择商品类型">
            <el-option label="健康服务" value="service" />
            <el-option label="保健品" value="supplement" />
            <el-option label="医疗器械" value="device" />
            <el-option label="体检套餐" value="checkup" />
          </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input-number v-model="createForm.quantity" :min="1" style="width: 100%" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input-number v-model="createForm.unitPrice" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="createForm.notes" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" :loading="createLoading" @click="handleCreate">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  ShoppingBag,
  Money,
  Clock,
  Check,
  ArrowDown
} from '@element-plus/icons-vue'
import { formatDate, formatAmount } from '@/utils'
import type { PurchaseOrder, PurchaseOrderQuery, CreatePurchaseOrderParams } from '@/types/purchase'
import type { Member } from '@/types/member'

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const currentOrder = ref<PurchaseOrder | null>(null)
const orders = ref<PurchaseOrder[]>([])
const stats = ref<any>(null)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const dateRange = ref<[string, string] | null>(null)
const memberOptions = ref<Member[]>([])

// 搜索表单
const searchForm = reactive<PurchaseOrderQuery>({
  orderNo: '',
  memberName: '',
  status: '',
  productType: '',
  startDate: '',
  endDate: ''
})

// 创建表单
const createForm = reactive<CreatePurchaseOrderParams>({
  memberId: 0,
  productName: '',
  productType: '',
  quantity: 1,
  unitPrice: 0,
  notes: ''
})

const createRules = {
  memberId: [{ required: true, message: '请选择会员', trigger: 'change' }],
  productName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  productType: [{ required: true, message: '请选择商品类型', trigger: 'change' }],
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }]
}

// 方法
const getProductTypeText = (type: string) => {
  const types = {
    service: '健康服务',
    supplement: '保健品',
    device: '医疗器械',
    checkup: '体检套餐'
  }
  return types[type] || type
}

const getStatusText = (status: string) => {
  const statuses = {
    pending: '待支付',
    paid: '已支付',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statuses[status] || status
}

const getStatusType = (status: string) => {
  const types = {
    pending: 'warning',
    paid: 'primary',
    shipped: 'info',
    completed: 'success',
    cancelled: 'info',
    refunded: 'danger'
  }
  return types[status] || 'info'
}

const handleSearch = () => {
  if (dateRange.value) {
    searchForm.startDate = dateRange.value[0]
    searchForm.endDate = dateRange.value[1]
  }
  currentPage.value = 1
  fetchOrders()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    orderNo: '',
    memberName: '',
    status: '',
    productType: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = null
  currentPage.value = 1
  fetchOrders()
}

const fetchOrders = async () => {
  try {
    loading.value = true
    // 模拟API调用
    const mockOrders = [
      {
        id: 1,
        orderNo: 'ORD202401250001',
        memberId: 1,
        memberName: '张三',
        memberPhone: '13800138001',
        productName: '高血压健康管理服务',
        productType: 'service',
        quantity: 1,
        unitPrice: 299.00,
        totalAmount: 299.00,
        status: 'completed',
        paymentMethod: '微信支付',
        createdAt: '2024-01-25 10:30:00',
        paidAt: '2024-01-25 10:35:00',
        notes: '包含3个月健康监测',
        statusHistory: [
          {
            id: 1,
            status: 'pending',
            operatorName: '系统',
            createdAt: '2024-01-25 10:30:00',
            notes: '订单创建'
          },
          {
            id: 2,
            status: 'paid',
            operatorName: '系统',
            createdAt: '2024-01-25 10:35:00',
            notes: '微信支付成功'
          },
          {
            id: 3,
            status: 'completed',
            operatorName: '管家小王',
            createdAt: '2024-01-25 11:00:00',
            notes: '服务开始执行'
          }
        ]
      }
    ]
    orders.value = mockOrders
    total.value = mockOrders.length
  } catch (error) {
    console.error('获取订单列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    // 模拟统计数据
    stats.value = {
      totalOrders: 156,
      totalAmount: 45680.00,
      pendingOrders: 8,
      completedOrders: 132
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const refreshData = () => {
  fetchOrders()
  fetchStats()
}

const viewOrder = (order: PurchaseOrder) => {
  currentOrder.value = order
  showDetailDialog.value = true
}

const processPayment = (order: PurchaseOrder) => {
  ElMessage.info('支付处理功能开发中')
}

const shipOrder = (order: PurchaseOrder) => {
  ElMessage.info('发货功能开发中')
}

const cancelOrder = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm('确定要取消此订单吗？', '确认取消', {
      type: 'warning'
    })
    ElMessage.success('订单已取消')
    fetchOrders()
  } catch (error) {
    // 用户取消
  }
}

const generateReminder = (order: PurchaseOrder) => {
  ElMessage.info('生成提醒功能开发中')
}

const exportOrder = (order: PurchaseOrder) => {
  ElMessage.info('导出订单功能开发中')
}

const searchMembers = async (query: string) => {
  if (!query) return
  // 模拟搜索会员
  memberOptions.value = [
    { id: 1, name: '张三', phone: '13800138001' },
    { id: 2, name: '李四', phone: '13800138002' }
  ]
}

const handleCreate = async () => {
  try {
    createLoading.value = true
    // 模拟创建订单
    ElMessage.success('订单创建成功')
    showCreateDialog.value = false
    fetchOrders()
  } catch (error) {
    ElMessage.error('创建订单失败')
  } finally {
    createLoading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOrders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchOrders()
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.purchase-records {
  padding: 20px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h3 {
  margin: 0 0 5px 0;
  color: var(--el-text-color-primary);
}

.header-left p {
  margin: 0;
  color: var(--el-text-color-secondary);
}

.header-right {
  display: flex;
  gap: 10px;
}

.search-section {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.total-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.amount-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.pending-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.completed-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.amount-text {
  font-weight: 600;
  color: var(--el-color-primary);
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.order-detail {
  max-height: 600px;
  overflow-y: auto;
}

.order-history {
  margin-top: 30px;
}

.order-history h4 {
  margin: 0 0 20px 0;
  color: var(--el-text-color-primary);
}

.history-content {
  background: var(--el-bg-color-page);
  padding: 12px;
  border-radius: 6px;
}

.history-status {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.history-notes {
  font-size: 12px;
  color: var(--el-text-color-regular);
}
</style>
