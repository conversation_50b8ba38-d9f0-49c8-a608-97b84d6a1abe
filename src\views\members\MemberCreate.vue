<template>
  <div class="member-create-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
        <div class="header-title">
          <h1>新建会员档案</h1>
          <p>请填写会员的基本信息和健康状况</p>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-section">
      <el-card shadow="never">
        <el-form
          ref="memberFormRef"
          :model="memberForm"
          :rules="memberRules"
          label-width="120px"
          size="large"
        >
          <!-- 基本信息 -->
          <div class="form-section-title">
            <h3>👤 基本信息</h3>
          </div>
          
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="memberForm.name"
                  placeholder="请输入会员姓名"
                  clearable
                />
              </el-form-item>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="memberForm.gender">
                  <el-radio :label="1">男</el-radio>
                  <el-radio :label="2">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="年龄" prop="age">
                <el-input-number
                  v-model="memberForm.age"
                  :min="1"
                  :max="120"
                  placeholder="请输入年龄"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input
                  v-model="memberForm.phone"
                  placeholder="请输入11位手机号码"
                  clearable
                />
              </el-form-item>
            </el-col>
            
            <el-col :xs="24" :sm="12">
              <el-form-item label="身份证号" prop="idCard">
                <el-input
                  v-model="memberForm.idCard"
                  placeholder="请输入身份证号码（可选）"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12">
              <el-form-item label="紧急联系人">
                <el-input
                  v-model="memberForm.emergencyContact"
                  placeholder="请输入紧急联系人姓名"
                  clearable
                />
              </el-form-item>
            </el-col>
            
            <el-col :xs="24" :sm="12">
              <el-form-item label="紧急联系电话">
                <el-input
                  v-model="memberForm.emergencyPhone"
                  placeholder="请输入紧急联系人电话"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="居住地址">
            <el-input
              v-model="memberForm.address"
              type="textarea"
              :rows="2"
              placeholder="请输入详细居住地址"
            />
          </el-form-item>

          <!-- 健康信息 -->
          <div class="form-section-title">
            <h3>🏥 健康信息</h3>
          </div>

          <el-form-item label="主诉" prop="chiefComplaint">
            <el-input
              v-model="memberForm.chiefComplaint"
              type="textarea"
              :rows="3"
              placeholder="请详细描述会员的主要健康问题或症状"
            />
          </el-form-item>

          <el-form-item label="健康标签">
            <el-select
              v-model="memberForm.healthTags"
              multiple
              filterable
              allow-create
              placeholder="请选择或输入健康标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in healthTagOptions"
                :key="tag.value"
                :label="tag.label"
                :value="tag.value"
              />
            </el-select>
            <div class="form-tip">
              可选择常用标签或自定义输入，如：高血压、糖尿病、心脏病等
            </div>
          </el-form-item>

          <!-- 头像上传 -->
          <div class="form-section-title">
            <h3>📷 头像上传</h3>
          </div>

          <el-form-item label="会员头像">
            <el-upload
              class="avatar-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :on-error="handleAvatarError"
              :before-upload="beforeAvatarUpload"
              accept="image/*"
            >
              <img v-if="memberForm.avatar" :src="memberForm.avatar" class="avatar" />
              <div v-else class="avatar-placeholder">
                <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传头像</div>
              </div>
            </el-upload>
            <div class="form-tip">
              支持 JPG、PNG 格式，文件大小不超过 5MB
            </div>
          </el-form-item>

          <!-- 多媒体文件上传 -->
          <div class="form-section-title">
            <h3>📁 相关文件</h3>
          </div>

          <el-form-item label="图片文件">
            <el-upload
              class="media-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :file-list="imageFileList"
              :on-success="handleImageSuccess"
              :on-remove="handleImageRemove"
              :before-upload="beforeImageUpload"
              accept="image/*"
              multiple
              list-type="picture-card"
            >
              <el-icon class="uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div class="form-tip">
              可上传会员的相关图片，如体检报告、化验单等，最多10张
            </div>
          </el-form-item>

          <el-form-item label="视频文件">
            <el-upload
              class="media-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :file-list="videoFileList"
              :on-success="handleVideoSuccess"
              :on-remove="handleVideoRemove"
              :before-upload="beforeVideoUpload"
              accept="video/*"
              multiple
            >
              <el-button type="primary" :icon="VideoCamera">
                上传视频
              </el-button>
            </el-upload>
            <div class="form-tip">
              可上传会员的相关视频，如症状记录等，单个文件不超过100MB，最多3个
            </div>
          </el-form-item>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button size="large" @click="goBack">
              取消
            </el-button>
            <el-button
              type="primary"
              size="large"
              :loading="submitting"
              @click="handleSubmit"
            >
              {{ submitting ? '创建中...' : '创建会员' }}
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules, type UploadProps } from 'element-plus'
import { ArrowLeft, Plus, VideoCamera } from '@element-plus/icons-vue'
import { createMember, uploadMemberMedia } from '@/api/member'
import { useUserStore } from '@/stores/user'
import type { MemberForm } from '@/types/member'

const router = useRouter()
const userStore = useUserStore()

const memberFormRef = ref<FormInstance>()
const submitting = ref(false)
const imageFileList = ref([])
const videoFileList = ref([])

// 表单数据
const memberForm = reactive<MemberForm>({
  name: '',
  gender: 1,
  age: 0,
  phone: '',
  chiefComplaint: '',
  avatar: '',
  idCard: '',
  emergencyContact: '',
  emergencyPhone: '',
  address: '',
  healthTags: []
})

// 健康标签选项
const healthTagOptions = ref([
  { label: '高血压', value: '高血压' },
  { label: '糖尿病', value: '糖尿病' },
  { label: '心脏病', value: '心脏病' },
  { label: '高血脂', value: '高血脂' },
  { label: '肥胖', value: '肥胖' },
  { label: '失眠', value: '失眠' },
  { label: '焦虑', value: '焦虑' },
  { label: '关节炎', value: '关节炎' }
])

// 表单验证规则
const memberRules: FormRules = {
  name: [
    { required: true, message: '请输入会员姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', min: 1, max: 120, message: '年龄必须在 1 到 120 之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  chiefComplaint: [
    { required: true, message: '请输入主诉', trigger: 'blur' },
    { min: 10, max: 500, message: '主诉长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 上传配置
const uploadAction = '/api/upload'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

// 头像上传处理
const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  memberForm.avatar = response.data.url
  ElMessage.success('头像上传成功')
}

const handleAvatarError: UploadProps['onError'] = () => {
  ElMessage.error('头像上传失败')
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 图片上传处理
const handleImageSuccess: UploadProps['onSuccess'] = (response, file) => {
  ElMessage.success('图片上传成功')
}

const handleImageRemove: UploadProps['onRemove'] = (file) => {
  ElMessage.info('图片已移除')
}

const beforeImageUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  if (imageFileList.value.length >= 10) {
    ElMessage.error('最多只能上传10张图片!')
    return false
  }
  return true
}

// 视频上传处理
const handleVideoSuccess: UploadProps['onSuccess'] = (response, file) => {
  ElMessage.success('视频上传成功')
}

const handleVideoRemove: UploadProps['onRemove'] = (file) => {
  ElMessage.info('视频已移除')
}

const beforeVideoUpload: UploadProps['beforeUpload'] = (file) => {
  const isVideo = file.type.startsWith('video/')
  const isLt100M = file.size / 1024 / 1024 < 100

  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return false
  }
  if (!isLt100M) {
    ElMessage.error('视频大小不能超过 100MB!')
    return false
  }
  if (videoFileList.value.length >= 3) {
    ElMessage.error('最多只能上传3个视频!')
    return false
  }
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!memberFormRef.value) return

  try {
    await memberFormRef.value.validate()
    submitting.value = true

    const response = await createMember(memberForm)
    
    ElMessage.success('会员创建成功')
    router.push(`/members/${response.data.id}`)
  } catch (error: any) {
    console.error('Create member failed:', error)
    ElMessage.error(error.message || '创建失败')
  } finally {
    submitting.value = false
  }
}

// 返回
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.member-create-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-title h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.header-title p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.form-section-title {
  margin: 32px 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--medical-blue);
}

.form-section-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--medical-blue);
  margin: 0;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.avatar-uploader {
  display: inline-block;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid var(--border-color);
}

.avatar-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.avatar-placeholder:hover {
  border-color: var(--primary-color);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #999;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

.media-uploader {
  width: 100%;
}

.uploader-icon {
  font-size: 28px;
  color: #999;
}

.form-actions {
  margin-top: 40px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-create-container {
    padding: 12px;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .form-actions .el-button {
    width: 100%;
    margin: 8px 0;
  }
}
</style>
