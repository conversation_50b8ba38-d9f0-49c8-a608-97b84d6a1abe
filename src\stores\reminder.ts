import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  getReminderList, 
  getReminderStatistics, 
  getUnreadReminderCount,
  updateReminderStatus,
  batchOperateReminders
} from '@/api/reminder'
import type { WorkReminder, ReminderStatistics, ReminderSearchParams } from '@/types'

export const useReminderStore = defineStore('reminder', () => {
  const reminders = ref<WorkReminder[]>([])
  const statistics = ref<ReminderStatistics | null>(null)
  const unreadCount = ref(0)
  const loading = ref(false)

  // 计算属性
  const urgentReminders = computed(() => 
    reminders.value.filter(r => r.priority === 1 && r.status === 0)
  )

  const todayReminders = computed(() => {
    const today = new Date().toDateString()
    return reminders.value.filter(r => 
      new Date(r.createdAt).toDateString() === today
    )
  })

  // 获取提醒列表
  const fetchReminders = async (params?: ReminderSearchParams) => {
    loading.value = true
    try {
      const response = await getReminderList(params || {
        page: 1,
        limit: 20
      })
      reminders.value = response.data.list
      return response
    } catch (error) {
      console.error('Failed to fetch reminders:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取提醒统计
  const fetchStatistics = async () => {
    try {
      const response = await getReminderStatistics()
      statistics.value = response.data
      return response
    } catch (error) {
      console.error('Failed to fetch reminder statistics:', error)
      throw error
    }
  }

  // 获取未读数量
  const fetchUnreadCount = async () => {
    try {
      const response = await getUnreadReminderCount()
      unreadCount.value = response.data.count
      return response
    } catch (error) {
      console.error('Failed to fetch unread count:', error)
      throw error
    }
  }

  // 更新提醒状态
  const updateReminder = async (id: number, data: {
    status: number
    note?: string
    estimatedCompletion?: string
  }) => {
    try {
      const response = await updateReminderStatus(id, data)
      
      // 更新本地数据
      const index = reminders.value.findIndex(r => r.id === id)
      if (index !== -1) {
        reminders.value[index] = response.data
      }
      
      // 更新未读数量
      if (data.status === 1 || data.status === 3) {
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
      
      return response
    } catch (error) {
      console.error('Failed to update reminder:', error)
      throw error
    }
  }

  // 批量操作提醒
  const batchOperate = async (action: string, reminderIds: number[], params?: any) => {
    try {
      const response = await batchOperateReminders({
        action: action as any,
        reminderIds,
        params
      })
      
      // 重新获取数据
      await fetchReminders()
      await fetchUnreadCount()
      
      return response
    } catch (error) {
      console.error('Failed to batch operate reminders:', error)
      throw error
    }
  }

  // 标记所有提醒已读
  const markAllRead = async () => {
    const unreadIds = reminders.value
      .filter(r => r.status === 0)
      .map(r => r.id)
    
    if (unreadIds.length > 0) {
      await batchOperate('mark_read', unreadIds)
    }
  }

  // 添加新提醒（用于实时推送）
  const addReminder = (reminder: WorkReminder) => {
    reminders.value.unshift(reminder)
    if (reminder.status === 0) {
      unreadCount.value += 1
    }
  }

  // 清空数据
  const clearData = () => {
    reminders.value = []
    statistics.value = null
    unreadCount.value = 0
  }

  return {
    reminders,
    statistics,
    unreadCount,
    loading,
    urgentReminders,
    todayReminders,
    fetchReminders,
    fetchStatistics,
    fetchUnreadCount,
    updateReminder,
    batchOperate,
    markAllRead,
    addReminder,
    clearData
  }
})
