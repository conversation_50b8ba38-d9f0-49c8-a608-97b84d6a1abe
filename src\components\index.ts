/**
 * 全局组件注册
 */

import type { App } from 'vue'
import ReminderPanel from './ReminderPanel.vue'
import Loading from './Loading.vue'
import Empty from './Empty.vue'

const components = {
  ReminderPanel,
  Loading,
  Empty
}

export default {
  install(app: App) {
    Object.keys(components).forEach(key => {
      app.component(key, components[key as keyof typeof components])
    })
  }
}

export {
  ReminderPanel,
  Loading,
  Empty
}
