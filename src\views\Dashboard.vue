<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card shadow="never" class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ userInfo?.name }}</h2>
            <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
          </div>
          <div class="welcome-avatar">
            <el-avatar :src="userInfo?.avatar" :size="60">
              {{ userInfo?.name?.charAt(0) }}
            </el-avatar>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据概览 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon members">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalMembers }}</div>
                <div class="stat-label">管理会员</div>
              </div>
            </div>
            <div class="stat-trend">
              <span class="trend-text">较昨日 +{{ stats.newMembersToday }}</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon reminders">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pendingReminders }}</div>
                <div class="stat-label">待处理提醒</div>
              </div>
            </div>
            <div class="stat-trend">
              <span class="trend-text urgent">{{ stats.urgentReminders }} 紧急</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon visits">
                <el-icon><LocationInformation /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.visitsThisMonth }}</div>
                <div class="stat-label">本月回访</div>
              </div>
            </div>
            <div class="stat-trend">
              <span class="trend-text">完成率 {{ stats.visitCompletionRate }}%</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon prescriptions">
                <el-icon><Notebook /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.activePrescriptions }}</div>
                <div class="stat-label">执行中处方</div>
              </div>
            </div>
            <div class="stat-trend">
              <span class="trend-text">本周 +{{ stats.newPrescriptionsWeek }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧：工作提醒和待办事项 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card shadow="never" class="reminder-card">
          <template #header>
            <div class="card-header">
              <span>🔔 今日工作提醒</span>
              <el-button type="text" size="small" @click="goToReminders">
                查看全部
              </el-button>
            </div>
          </template>
          
          <div class="reminder-list">
            <div
              v-for="reminder in todayReminders"
              :key="reminder.id"
              class="reminder-item"
              :class="getPriorityClass(reminder.priority)"
              @click="handleReminderClick(reminder)"
            >
              <div class="reminder-dot"></div>
              <div class="reminder-content">
                <div class="reminder-title">{{ reminder.title }}</div>
                <div class="reminder-time">{{ formatTime(reminder.createdAt) }}</div>
              </div>
            </div>
            
            <div v-if="todayReminders.length === 0" class="empty-reminders">
              <el-empty description="今日暂无提醒" :image-size="60" />
            </div>
          </div>
        </el-card>

        <!-- 快速操作 -->
        <el-card shadow="never" class="quick-actions-card">
          <template #header>
            <span>⚡ 快速操作</span>
          </template>
          
          <div class="quick-actions">
            <el-button type="primary" :icon="Plus" @click="goToCreateMember">
              新建会员
            </el-button>
            <el-button type="success" :icon="DocumentAdd" @click="goToCreateVisit">
              创建回访
            </el-button>
            <el-button type="warning" :icon="ChatDotRound" @click="goToConsultations">
              会员咨询
            </el-button>
            <el-button type="info" :icon="DataAnalysis" @click="goToStatistics">
              查看统计
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 中间：健康指标趋势 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card shadow="never" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>📊 健康指标趋势</span>
              <el-select v-model="selectedIndicator" size="small" style="width: 120px">
                <el-option label="血压" value="blood_pressure" />
                <el-option label="血糖" value="blood_glucose" />
                <el-option label="体重" value="weight" />
              </el-select>
            </div>
          </template>
          
          <div class="chart-container">
            <v-chart
              :option="chartOption"
              :style="{ height: '300px' }"
              autoresize
            />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：最近活动 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="8">
        <el-card shadow="never" class="activity-card">
          <template #header>
            <span>📝 最近活动</span>
          </template>
          
          <el-timeline class="activity-timeline">
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="formatTime(activity.createdAt)"
              :type="getActivityType(activity.type)"
            >
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useReminderStore } from '@/stores/reminder'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import {
  User,
  Bell,
  LocationInformation,
  Notebook,
  Plus,
  DocumentAdd,
  ChatDotRound,
  DataAnalysis
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 注册ECharts组件
use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

const router = useRouter()
const userStore = useUserStore()
const reminderStore = useReminderStore()

const selectedIndicator = ref('blood_pressure')

// 计算属性
const userInfo = computed(() => userStore.userInfo)
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日 dddd'))
const todayReminders = computed(() => reminderStore.todayReminders.slice(0, 5))

// 模拟数据
const stats = ref({
  totalMembers: 156,
  newMembersToday: 3,
  pendingReminders: 12,
  urgentReminders: 2,
  visitsThisMonth: 45,
  visitCompletionRate: 92,
  activePrescriptions: 8,
  newPrescriptionsWeek: 5
})

const recentActivities = ref([
  {
    id: 1,
    type: 'member',
    title: '新建会员档案',
    description: '为张三建立了健康档案',
    createdAt: '2024-01-25T10:30:00Z'
  },
  {
    id: 2,
    type: 'visit',
    title: '完成回访',
    description: '完成了李四的定期回访',
    createdAt: '2024-01-25T09:15:00Z'
  },
  {
    id: 3,
    type: 'prescription',
    title: '执行处方单',
    description: '协助王五完成用药指导',
    createdAt: '2024-01-24T16:45:00Z'
  }
])

// 图表配置
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '平均值',
      type: 'line',
      data: [120, 118, 125, 122, 119, 121],
      smooth: true,
      itemStyle: {
        color: '#409eff'
      }
    }
  ]
}))

// 方法
const getPriorityClass = (priority: number) => {
  const classes = { 1: 'urgent', 2: 'important', 3: 'normal', 4: 'info' }
  return classes[priority as keyof typeof classes] || 'normal'
}

const getActivityType = (type: string) => {
  const types = { member: 'primary', visit: 'success', prescription: 'warning' }
  return types[type as keyof typeof types] || 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('HH:mm')
}

const handleReminderClick = (reminder: any) => {
  // 处理提醒点击
  console.log('Handle reminder:', reminder)
}

// 导航方法
const goToReminders = () => router.push('/reminders')
const goToCreateMember = () => router.push('/members/create')
const goToCreateVisit = () => router.push('/visits/create')
const goToConsultations = () => router.push('/consultations')
const goToStatistics = () => router.push('/statistics')

onMounted(() => {
  // 获取提醒数据
  reminderStore.fetchReminders({ page: 1, limit: 10 })
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-card {
  background: linear-gradient(135deg, var(--medical-blue) 0%, var(--medical-teal) 100%);
  color: white;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
}

.overview-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.members { background: var(--medical-blue); }
.stat-icon.reminders { background: var(--warning-color); }
.stat-icon.visits { background: var(--medical-green); }
.stat-icon.prescriptions { background: var(--medical-teal); }

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.stat-trend {
  font-size: 12px;
  color: #999;
}

.trend-text.urgent {
  color: var(--danger-color);
}

.main-content {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reminder-list {
  max-height: 300px;
  overflow-y: auto;
}

.reminder-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 8px;
}

.reminder-item:hover {
  background: #f8f9fa;
}

.reminder-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-color);
}

.reminder-item.urgent .reminder-dot {
  background: var(--danger-color);
}

.reminder-item.important .reminder-dot {
  background: var(--warning-color);
}

.reminder-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.reminder-time {
  font-size: 12px;
  color: #999;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.chart-container {
  padding: 10px 0;
}

.activity-timeline {
  max-height: 350px;
  overflow-y: auto;
}

.activity-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 12px;
  color: #666;
}

.empty-reminders {
  text-align: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
}
</style>
