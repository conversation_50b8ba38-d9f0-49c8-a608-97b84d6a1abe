/**
 * 文件管理状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  FileInfo,
  FileUploadParams,
  FileQuery,
  FileStats,
  FileBatchParams,
  FileShare,
  CompressionConfig,
  WatermarkConfig,
  UploadProgress,
  FileType,
  FileStatus
} from '@/types/file'
import {
  uploadFile,
  uploadMultipleFiles,
  getFileList,
  getFileDetail,
  updateFile,
  deleteFile,
  batchOperateFiles,
  getFileStats,
  validateFile,
  generatePreview,
  compressFile,
  addWatermark,
  createFileShare,
  downloadFile,
  getFileCategories,
  getFileTags,
  searchFiles,
  getRecentFiles
} from '@/api/file'

export const useFileStore = defineStore('file', () => {
  // 状态
  const files = ref<FileInfo[]>([])
  const currentFile = ref<FileInfo | null>(null)
  const uploadingFiles = ref<Map<string, UploadProgress>>(new Map())
  const stats = ref<FileStats | null>(null)
  const categories = ref<string[]>([])
  const tags = ref<string[]>([])
  const loading = ref(false)
  const total = ref(0)
  const query = ref<FileQuery>({
    page: 1,
    limit: 20
  })

  // 计算属性
  const imageFiles = computed(() => 
    files.value.filter(file => file.type === FileType.IMAGE)
  )

  const videoFiles = computed(() => 
    files.value.filter(file => file.type === FileType.VIDEO)
  )

  const audioFiles = computed(() => 
    files.value.filter(file => file.type === FileType.AUDIO)
  )

  const documentFiles = computed(() => 
    files.value.filter(file => file.type === FileType.DOCUMENT)
  )

  const uploadingCount = computed(() => uploadingFiles.value.size)

  const totalFileSize = computed(() => {
    return files.value.reduce((total, file) => total + file.size, 0)
  })

  const filesByCategory = computed(() => {
    const grouped = new Map<string, FileInfo[]>()
    files.value.forEach(file => {
      const category = file.category || '未分类'
      if (!grouped.has(category)) {
        grouped.set(category, [])
      }
      grouped.get(category)!.push(file)
    })
    return grouped
  })

  // 获取文件列表
  const fetchFiles = async (params?: FileQuery) => {
    try {
      loading.value = true
      if (params) {
        query.value = { ...query.value, ...params }
      }
      
      const response = await getFileList(query.value)
      files.value = response.list
      total.value = response.total
      
      return response
    } catch (error) {
      console.error('获取文件列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取文件详情
  const fetchFileDetail = async (fileId: string) => {
    try {
      loading.value = true
      const file = await getFileDetail(fileId)
      currentFile.value = file
      return file
    } catch (error) {
      console.error('获取文件详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 上传文件
  const uploadFileAction = async (params: FileUploadParams) => {
    try {
      const fileId = Date.now().toString()
      
      // 添加上传进度跟踪
      const onProgress = (progress: UploadProgress) => {
        uploadingFiles.value.set(fileId, { ...progress, fileId })
      }
      
      const file = await uploadFile(params, onProgress)
      
      // 移除上传进度跟踪
      uploadingFiles.value.delete(fileId)
      
      // 添加到文件列表
      files.value.unshift(file)
      total.value += 1
      
      return file
    } catch (error) {
      console.error('上传文件失败:', error)
      throw error
    }
  }

  // 批量上传文件
  const uploadMultipleFilesAction = async (
    fileList: File[],
    params: Omit<FileUploadParams, 'file'>
  ) => {
    try {
      const uploadedFiles = await uploadMultipleFiles(fileList, params)
      
      // 添加到文件列表
      files.value.unshift(...uploadedFiles)
      total.value += uploadedFiles.length
      
      return uploadedFiles
    } catch (error) {
      console.error('批量上传文件失败:', error)
      throw error
    }
  }

  // 更新文件信息
  const updateFileAction = async (fileId: string, data: Partial<FileInfo>) => {
    try {
      const file = await updateFile(fileId, data)
      const index = files.value.findIndex(f => f.id === fileId)
      if (index !== -1) {
        files.value[index] = file
      }
      if (currentFile.value?.id === fileId) {
        currentFile.value = file
      }
      return file
    } catch (error) {
      console.error('更新文件信息失败:', error)
      throw error
    }
  }

  // 删除文件
  const deleteFileAction = async (fileId: string) => {
    try {
      await deleteFile(fileId)
      const index = files.value.findIndex(f => f.id === fileId)
      if (index !== -1) {
        files.value.splice(index, 1)
        total.value -= 1
      }
      if (currentFile.value?.id === fileId) {
        currentFile.value = null
      }
    } catch (error) {
      console.error('删除文件失败:', error)
      throw error
    }
  }

  // 批量操作文件
  const batchOperateFilesAction = async (params: FileBatchParams) => {
    try {
      await batchOperateFiles(params)
      
      // 根据操作类型更新本地状态
      if (params.action === 'delete') {
        files.value = files.value.filter(file => !params.fileIds.includes(file.id))
        total.value -= params.fileIds.length
      } else if (params.action === 'category' && params.targetCategory) {
        files.value.forEach(file => {
          if (params.fileIds.includes(file.id)) {
            file.category = params.targetCategory
          }
        })
      } else if (params.action === 'tag' && params.tags) {
        files.value.forEach(file => {
          if (params.fileIds.includes(file.id)) {
            file.tags = params.tags
          }
        })
      }
    } catch (error) {
      console.error('批量操作文件失败:', error)
      throw error
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const statistics = await getFileStats()
      stats.value = statistics
      return statistics
    } catch (error) {
      console.error('获取文件统计失败:', error)
      throw error
    }
  }

  // 验证文件
  const validateFileAction = async (file: File, config?: FileUploadParams) => {
    try {
      const result = await validateFile(file, config)
      return result
    } catch (error) {
      console.error('验证文件失败:', error)
      throw error
    }
  }

  // 生成预览
  const generatePreviewAction = async (fileId: string) => {
    try {
      const result = await generatePreview(fileId)
      
      // 更新文件的预览URL
      const index = files.value.findIndex(f => f.id === fileId)
      if (index !== -1) {
        files.value[index].previewUrl = result.previewUrl
      }
      
      return result
    } catch (error) {
      console.error('生成预览失败:', error)
      throw error
    }
  }

  // 压缩文件
  const compressFileAction = async (fileId: string, config: CompressionConfig) => {
    try {
      const file = await compressFile(fileId, config)
      
      // 更新文件信息
      const index = files.value.findIndex(f => f.id === fileId)
      if (index !== -1) {
        files.value[index] = file
      }
      
      return file
    } catch (error) {
      console.error('压缩文件失败:', error)
      throw error
    }
  }

  // 添加水印
  const addWatermarkAction = async (fileId: string, config: WatermarkConfig) => {
    try {
      const file = await addWatermark(fileId, config)
      
      // 更新文件信息
      const index = files.value.findIndex(f => f.id === fileId)
      if (index !== -1) {
        files.value[index] = file
      }
      
      return file
    } catch (error) {
      console.error('添加水印失败:', error)
      throw error
    }
  }

  // 创建分享
  const createShareAction = async (fileId: string, config: Partial<FileShare>) => {
    try {
      const share = await createFileShare(fileId, config)
      return share
    } catch (error) {
      console.error('创建分享失败:', error)
      throw error
    }
  }

  // 下载文件
  const downloadFileAction = async (fileId: string, fileName?: string) => {
    try {
      const blob = await downloadFile(fileId, fileName)
      return blob
    } catch (error) {
      console.error('下载文件失败:', error)
      throw error
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const categoryList = await getFileCategories()
      categories.value = categoryList
      return categoryList
    } catch (error) {
      console.error('获取分类列表失败:', error)
      throw error
    }
  }

  // 获取标签列表
  const fetchTags = async () => {
    try {
      const tagList = await getFileTags()
      tags.value = tagList
      return tagList
    } catch (error) {
      console.error('获取标签列表失败:', error)
      throw error
    }
  }

  // 搜索文件
  const searchFilesAction = async (keyword: string, filters?: Partial<FileQuery>) => {
    try {
      const response = await searchFiles(keyword, filters)
      return response
    } catch (error) {
      console.error('搜索文件失败:', error)
      throw error
    }
  }

  // 获取最近文件
  const fetchRecentFiles = async (limit: number = 10) => {
    try {
      const recentFiles = await getRecentFiles(limit)
      return recentFiles
    } catch (error) {
      console.error('获取最近文件失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    files.value = []
    currentFile.value = null
    uploadingFiles.value.clear()
    stats.value = null
    categories.value = []
    tags.value = []
    total.value = 0
    query.value = { page: 1, limit: 20 }
  }

  return {
    // 状态
    files,
    currentFile,
    uploadingFiles,
    stats,
    categories,
    tags,
    loading,
    total,
    query,
    
    // 计算属性
    imageFiles,
    videoFiles,
    audioFiles,
    documentFiles,
    uploadingCount,
    totalFileSize,
    filesByCategory,
    
    // 方法
    fetchFiles,
    fetchFileDetail,
    uploadFileAction,
    uploadMultipleFilesAction,
    updateFileAction,
    deleteFileAction,
    batchOperateFilesAction,
    fetchStats,
    validateFileAction,
    generatePreviewAction,
    compressFileAction,
    addWatermarkAction,
    createShareAction,
    downloadFileAction,
    fetchCategories,
    fetchTags,
    searchFilesAction,
    fetchRecentFiles,
    resetState
  }
})
