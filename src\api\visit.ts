/**
 * 回访相关API接口
 */

import { request } from '@/utils/request'
import type {
  Visit,
  VisitPlan,
  VisitQuery,
  VisitStats,
  VisitReminder,
  VisitTemplate,
  CreateVisitParams,
  UpdateVisitParams,
  LocationInfo
} from '@/types/visit'
import type { ApiResponse, PaginationResult } from '@/types/api'

/**
 * 获取回访记录列表
 */
export const getVisitList = (params: VisitQuery = {}) => {
  return request<PaginationResult<Visit>>({
    url: '/visits',
    method: 'GET',
    params
  })
}

/**
 * 获取回访记录详情
 */
export const getVisitDetail = (id: number) => {
  return request<Visit>({
    url: `/visits/${id}`,
    method: 'GET'
  })
}

/**
 * 创建回访记录
 */
export const createVisit = (data: CreateVisitParams) => {
  const formData = new FormData()
  
  // 添加基本字段
  Object.keys(data).forEach(key => {
    const value = data[key as keyof CreateVisitParams]
    if (value !== undefined && !['images', 'videos', 'audios'].includes(key)) {
      if (typeof value === 'object') {
        formData.append(key, JSON.stringify(value))
      } else {
        formData.append(key, String(value))
      }
    }
  })
  
  // 添加文件
  data.images?.forEach((file, index) => {
    formData.append(`images[${index}]`, file)
  })
  data.videos?.forEach((file, index) => {
    formData.append(`videos[${index}]`, file)
  })
  data.audios?.forEach((file, index) => {
    formData.append(`audios[${index}]`, file)
  })
  
  return request<Visit>({
    url: '/visits',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 更新回访记录
 */
export const updateVisit = (id: number, data: UpdateVisitParams) => {
  return request<Visit>({
    url: `/visits/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除回访记录
 */
export const deleteVisit = (id: number) => {
  return request<void>({
    url: `/visits/${id}`,
    method: 'DELETE'
  })
}

/**
 * 开始回访
 */
export const startVisit = (id: number, location?: LocationInfo) => {
  return request<Visit>({
    url: `/visits/${id}/start`,
    method: 'POST',
    data: { location }
  })
}

/**
 * 结束回访
 */
export const endVisit = (id: number, location?: LocationInfo) => {
  return request<Visit>({
    url: `/visits/${id}/end`,
    method: 'POST',
    data: { location }
  })
}

/**
 * 获取回访统计信息
 */
export const getVisitStats = () => {
  return request<VisitStats>({
    url: '/visits/stats',
    method: 'GET'
  })
}

/**
 * 获取今日回访列表
 */
export const getTodayVisits = () => {
  return request<Visit[]>({
    url: '/visits/today',
    method: 'GET'
  })
}

/**
 * 获取逾期回访列表
 */
export const getOverdueVisits = () => {
  return request<Visit[]>({
    url: '/visits/overdue',
    method: 'GET'
  })
}

/**
 * 获取即将到期的回访
 */
export const getUpcomingVisits = (days: number = 3) => {
  return request<Visit[]>({
    url: '/visits/upcoming',
    method: 'GET',
    params: { days }
  })
}

/**
 * 获取回访计划列表
 */
export const getVisitPlans = (memberId?: number) => {
  return request<VisitPlan[]>({
    url: '/visit-plans',
    method: 'GET',
    params: { memberId }
  })
}

/**
 * 创建回访计划
 */
export const createVisitPlan = (data: Omit<VisitPlan, 'id' | 'createdAt' | 'updatedAt'>) => {
  return request<VisitPlan>({
    url: '/visit-plans',
    method: 'POST',
    data
  })
}

/**
 * 更新回访计划
 */
export const updateVisitPlan = (id: number, data: Partial<VisitPlan>) => {
  return request<VisitPlan>({
    url: `/visit-plans/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除回访计划
 */
export const deleteVisitPlan = (id: number) => {
  return request<void>({
    url: `/visit-plans/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取回访提醒列表
 */
export const getVisitReminders = () => {
  return request<VisitReminder[]>({
    url: '/visit-reminders',
    method: 'GET'
  })
}

/**
 * 标记提醒已处理
 */
export const markReminderProcessed = (id: number) => {
  return request<void>({
    url: `/visit-reminders/${id}/processed`,
    method: 'POST'
  })
}

/**
 * 获取回访模板列表
 */
export const getVisitTemplates = (visitType?: number) => {
  return request<VisitTemplate[]>({
    url: '/visit-templates',
    method: 'GET',
    params: { visitType }
  })
}

/**
 * 获取当前位置
 */
export const getCurrentLocation = (): Promise<LocationInfo> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('浏览器不支持地理定位'))
      return
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude, accuracy } = position.coords
        
        try {
          // 调用逆地理编码API获取地址
          const address = await reverseGeocode(latitude, longitude)
          
          resolve({
            latitude,
            longitude,
            address,
            accuracy,
            timestamp: new Date().toISOString()
          })
        } catch (error) {
          // 如果逆地理编码失败，返回坐标信息
          resolve({
            latitude,
            longitude,
            address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
            accuracy,
            timestamp: new Date().toISOString()
          })
        }
      },
      (error) => {
        reject(new Error(`定位失败: ${error.message}`))
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )
  })
}

/**
 * 逆地理编码 - 将坐标转换为地址
 */
export const reverseGeocode = (latitude: number, longitude: number): Promise<string> => {
  return request<{ address: string }>({
    url: '/location/reverse-geocode',
    method: 'GET',
    params: { latitude, longitude }
  }).then(response => response.address)
}

/**
 * 上传回访媒体文件
 */
export const uploadVisitMedia = (file: File, type: 'image' | 'video' | 'audio') => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)
  
  return request<{ url: string; thumbnail?: string }>({
    url: '/visits/upload-media',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出回访数据
 */
export const exportVisits = (params: VisitQuery = {}) => {
  return request<Blob>({
    url: '/visits/export',
    method: 'GET',
    params,
    responseType: 'blob'
  })
}
