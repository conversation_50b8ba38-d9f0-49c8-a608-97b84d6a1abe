<template>
  <div class="prescription-detail">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else-if="prescription" class="detail-content">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
          <div class="title-info">
            <h2>处方单详情</h2>
            <p>处方单号：{{ prescription.prescriptionNo }}</p>
          </div>
        </div>
        <div class="header-right">
          <el-tag :type="getStatusType(prescription.status)" size="large">
            {{ getStatusText(prescription.status) }}
          </el-tag>
          <el-tag :type="getPriorityType(prescription.priority)" size="large">
            {{ getPriorityText(prescription.priority) }}
          </el-tag>
        </div>
      </div>

      <!-- 基本信息 -->
      <el-card class="info-card" header="基本信息">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>会员姓名：</label>
              <span>{{ prescription.memberName }}</span>
            </div>
            <div class="info-item">
              <label>医生姓名：</label>
              <span>{{ prescription.doctorName }}</span>
            </div>
            <div class="info-item">
              <label>医院科室：</label>
              <span>{{ prescription.hospitalName }} - {{ prescription.department }}</span>
            </div>
            <div class="info-item">
              <label>诊断结果：</label>
              <span>{{ prescription.diagnosis }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>开具时间：</label>
              <span>{{ formatDate(prescription.issuedAt, 'YYYY-MM-DD HH:mm') }}</span>
            </div>
            <div class="info-item">
              <label>过期时间：</label>
              <span :class="{ 'text-danger': isExpiringSoon(prescription.expiresAt) }">
                {{ formatDate(prescription.expiresAt, 'YYYY-MM-DD HH:mm') }}
              </span>
            </div>
            <div class="info-item">
              <label>执行时间：</label>
              <span>{{ prescription.executedAt ? formatDate(prescription.executedAt, 'YYYY-MM-DD HH:mm') : '未执行' }}</span>
            </div>
            <div class="info-item">
              <label>完成时间：</label>
              <span>{{ prescription.completedAt ? formatDate(prescription.completedAt, 'YYYY-MM-DD HH:mm') : '未完成' }}</span>
            </div>
          </el-col>
        </el-row>
        <div v-if="prescription.notes" class="info-item full-width">
          <label>备注说明：</label>
          <p>{{ prescription.notes }}</p>
        </div>
      </el-card>

      <!-- 药品信息 -->
      <el-card class="medication-card" header="药品信息">
        <el-table :data="prescription.medications" border>
          <el-table-column prop="name" label="药品名称" width="200" />
          <el-table-column prop="specification" label="规格" width="120" />
          <el-table-column prop="dosage" label="用法用量" width="150" />
          <el-table-column prop="frequency" label="服用频率" width="120" />
          <el-table-column prop="duration" label="服用时长" width="120" />
          <el-table-column prop="notes" label="备注" show-overflow-tooltip />
        </el-table>
      </el-card>

      <!-- 附件信息 -->
      <el-card v-if="prescription.attachments?.length" class="attachment-card" header="相关附件">
        <div class="attachment-list">
          <div v-for="(attachment, index) in prescription.attachments" :key="index" class="attachment-item">
            <el-image
              v-if="isImage(attachment)"
              :src="attachment"
              :preview-src-list="imageAttachments"
              class="attachment-image"
              fit="cover"
            />
            <div v-else class="attachment-file">
              <el-icon><Document /></el-icon>
              <span>{{ getFileName(attachment) }}</span>
              <el-button type="primary" size="small" @click="downloadFile(attachment)">下载</el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 执行记录 -->
      <el-card class="execution-card" header="执行记录">
        <div v-if="executions.length === 0" class="empty-state">
          <el-empty description="暂无执行记录" />
        </div>
        <div v-else class="execution-list">
          <div v-for="execution in executions" :key="execution.id" class="execution-item">
            <div class="execution-header">
              <div class="executor-info">
                <el-avatar :size="40">{{ execution.butlerName.charAt(0) }}</el-avatar>
                <div class="executor-details">
                  <div class="executor-name">{{ execution.butlerName }}</div>
                  <div class="execution-time">{{ formatDate(execution.executedAt, 'YYYY-MM-DD HH:mm') }}</div>
                </div>
              </div>
              <div v-if="execution.satisfactionScore" class="satisfaction">
                <el-rate v-model="execution.satisfactionScore" disabled />
              </div>
            </div>
            <div class="execution-content">
              <div class="execution-section">
                <h4>执行计划</h4>
                <p>{{ execution.executionPlan }}</p>
              </div>
              <div class="execution-section">
                <h4>执行说明</h4>
                <p>{{ execution.executionNotes }}</p>
              </div>
              <div class="execution-section">
                <h4>执行结果</h4>
                <p>{{ execution.executionResult }}</p>
              </div>
              <div v-if="execution.memberFeedback" class="execution-section">
                <h4>会员反馈</h4>
                <p>{{ execution.memberFeedback }}</p>
              </div>
              <div v-if="execution.executionImages?.length" class="execution-images">
                <h4>执行图片</h4>
                <div class="image-list">
                  <el-image
                    v-for="(image, index) in execution.executionImages"
                    :key="index"
                    :src="image"
                    :preview-src-list="execution.executionImages"
                    class="execution-image"
                    fit="cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button v-if="prescription.status === 1" type="primary" size="large" @click="executePrescription">
          执行处方单
        </el-button>
        <el-button v-if="prescription.status === 1" type="warning" @click="cancelPrescription">
          取消处方单
        </el-button>
        <el-button type="info" @click="editPrescription">
          编辑处方单
        </el-button>
        <el-button @click="printPrescription">
          打印处方单
        </el-button>
      </div>
    </div>

    <!-- 执行处方单对话框 -->
    <PrescriptionExecuteDialog
      v-model="showExecuteDialog"
      :prescription-id="prescription?.id"
      @success="handleExecuteSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Document } from '@element-plus/icons-vue'
import { usePrescriptionStore } from '@/stores/prescription'
import { formatDate } from '@/utils'
import PrescriptionExecuteDialog from './components/PrescriptionExecuteDialog.vue'

const route = useRoute()
const router = useRouter()
const prescriptionStore = usePrescriptionStore()

// 响应式数据
const showExecuteDialog = ref(false)
const prescriptionId = ref(Number(route.params.id))

// 计算属性
const prescription = computed(() => prescriptionStore.currentPrescription)
const executions = computed(() => prescriptionStore.executions)
const loading = computed(() => prescriptionStore.loading)

const imageAttachments = computed(() => {
  return prescription.value?.attachments?.filter(isImage) || []
})

// 方法
const goBack = () => {
  router.back()
}

const fetchData = async () => {
  try {
    await prescriptionStore.fetchPrescriptionDetail(prescriptionId.value)
    await prescriptionStore.fetchExecutions(prescriptionId.value)
  } catch (error) {
    ElMessage.error('获取处方单详情失败')
    goBack()
  }
}

const executePrescription = () => {
  showExecuteDialog.value = true
}

const cancelPrescription = async () => {
  try {
    await ElMessageBox.confirm('确定要取消这个处方单吗？', '确认取消', {
      type: 'warning'
    })
    
    await prescriptionStore.updatePrescriptionAction(prescriptionId.value, { status: 5 })
    ElMessage.success('处方单已取消')
    fetchData()
  } catch (error) {
    // 用户取消操作
  }
}

const editPrescription = () => {
  // 跳转到编辑页面
  router.push(`/prescriptions/${prescriptionId.value}/edit`)
}

const printPrescription = () => {
  window.print()
}

const handleExecuteSuccess = () => {
  showExecuteDialog.value = false
  fetchData()
}

// 辅助方法
const getPriorityType = (priority: number) => {
  const types = ['', 'info', '', 'warning', 'danger']
  return types[priority] || 'info'
}

const getPriorityText = (priority: number) => {
  const texts = ['', '普通', '一般', '重要', '紧急']
  return texts[priority] || '未知'
}

const getStatusType = (status: number) => {
  const types = ['', 'warning', 'primary', 'success', 'danger', 'info']
  return types[status] || 'info'
}

const getStatusText = (status: number) => {
  const texts = ['', '待执行', '执行中', '已完成', '已过期', '已取消']
  return texts[status] || '未知'
}

const isExpiringSoon = (expiresAt: string) => {
  const now = new Date()
  const expires = new Date(expiresAt)
  const diff = expires.getTime() - now.getTime()
  return diff <= 24 * 60 * 60 * 1000
}

const isImage = (url: string) => {
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(url)
}

const getFileName = (url: string) => {
  return url.split('/').pop() || '未知文件'
}

const downloadFile = (url: string) => {
  const link = document.createElement('a')
  link.href = url
  link.download = getFileName(url)
  link.click()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.prescription-detail {
  padding: 20px;
}

.loading-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.title-info h2 {
  margin: 0 0 5px 0;
  color: var(--text-color-primary);
}

.title-info p {
  margin: 0;
  color: var(--text-color-secondary);
}

.header-right {
  display: flex;
  gap: 10px;
}

.info-card,
.medication-card,
.attachment-card,
.execution-card {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
}

.info-item.full-width {
  flex-direction: column;
}

.info-item label {
  font-weight: 600;
  color: var(--text-color-primary);
  min-width: 100px;
  margin-right: 10px;
}

.info-item span,
.info-item p {
  color: var(--text-color-regular);
  margin: 0;
}

.attachment-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.attachment-image {
  width: 200px;
  height: 150px;
  border-radius: 8px;
}

.attachment-file {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.execution-item {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.executor-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.executor-name {
  font-weight: 600;
  color: var(--text-color-primary);
}

.execution-time {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.execution-section {
  margin-bottom: 15px;
}

.execution-section h4 {
  margin: 0 0 8px 0;
  color: var(--text-color-primary);
  font-size: 14px;
}

.execution-section p {
  margin: 0;
  color: var(--text-color-regular);
  line-height: 1.6;
}

.image-list {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.execution-image {
  width: 120px;
  height: 90px;
  border-radius: 6px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.text-danger {
  color: var(--danger-color);
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>
