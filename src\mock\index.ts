/**
 * 模拟数据配置
 */

import { mockMembers } from './member'
import { mockReminders } from './reminder'
import { mockQuestionnaires } from './questionnaire'
import { mockStatistics } from './statistics'

// 是否启用模拟数据
export const isMockEnabled = import.meta.env.VITE_DEV_MOCK === 'true'

// 模拟API延迟
export const mockDelay = (ms: number = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 模拟API响应
export const mockResponse = <T>(data: T, success: boolean = true) => {
  return {
    code: success ? 200 : 500,
    message: success ? '操作成功' : '操作失败',
    data,
    timestamp: Date.now()
  }
}

// 分页数据处理
export const mockPagination = <T>(
  list: T[],
  page: number = 1,
  limit: number = 20
) => {
  const start = (page - 1) * limit
  const end = start + limit
  
  return {
    list: list.slice(start, end),
    total: list.length,
    page,
    limit,
    totalPages: Math.ceil(list.length / limit)
  }
}

// 导出所有模拟数据
export {
  mockMembers,
  mockReminders,
  mockQuestionnaires,
  mockStatistics
}
