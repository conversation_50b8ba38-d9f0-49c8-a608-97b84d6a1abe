/**
 * 实时通知相关类型定义
 */

// 通知类型
export enum NotificationType {
  PRESCRIPTION = 'prescription',        // 处方单通知
  VISIT = 'visit',                     // 回访通知
  HEALTH_ALERT = 'health_alert',       // 健康异常预警
  QUESTIONNAIRE = 'questionnaire',     // 问卷通知
  MEMBER_CONSULTATION = 'member_consultation', // 会员咨询
  SYSTEM = 'system',                   // 系统通知
  REMINDER = 'reminder'                // 工作提醒
}

// 通知优先级
export enum NotificationPriority {
  LOW = 1,        // 低优先级
  NORMAL = 2,     // 普通
  HIGH = 3,       // 高优先级
  URGENT = 4,     // 紧急
  CRITICAL = 5    // 危急
}

// 通知状态
export enum NotificationStatus {
  UNREAD = 1,     // 未读
  READ = 2,       // 已读
  PROCESSED = 3,  // 已处理
  DISMISSED = 4   // 已忽略
}

// 推送渠道
export enum PushChannel {
  BROWSER = 'browser',          // 浏览器通知
  WEBSOCKET = 'websocket',      // WebSocket实时推送
  EMAIL = 'email',              // 邮件通知
  SMS = 'sms',                  // 短信通知
  WECHAT = 'wechat',           // 微信通知
  VOICE = 'voice'              // 语音通知
}

// 通知信息
export interface Notification {
  id: string
  type: NotificationType           // 通知类型
  priority: NotificationPriority   // 优先级
  status: NotificationStatus       // 状态
  
  // 内容信息
  title: string                    // 标题
  content: string                  // 内容
  summary?: string                 // 摘要
  icon?: string                    // 图标
  image?: string                   // 图片
  
  // 关联信息
  relatedType?: string             // 关联类型
  relatedId?: string               // 关联ID
  relatedData?: any                // 关联数据
  
  // 接收者信息
  recipientId: string              // 接收者ID
  recipientType: 'user' | 'role' | 'group'  // 接收者类型
  
  // 推送配置
  channels: PushChannel[]          // 推送渠道
  pushAt?: string                  // 推送时间
  expiresAt?: string               // 过期时间
  
  // 操作信息
  actions?: NotificationAction[]   // 可执行操作
  
  // 元数据
  metadata?: Record<string, any>   // 元数据
  
  // 时间信息
  createdAt: string
  readAt?: string                  // 阅读时间
  processedAt?: string             // 处理时间
  dismissedAt?: string             // 忽略时间
}

// 通知操作
export interface NotificationAction {
  id: string
  label: string                    // 操作标签
  type: 'primary' | 'secondary' | 'danger'  // 操作类型
  url?: string                     // 跳转链接
  action?: string                  // 操作名称
  data?: any                       // 操作数据
}

// 通知设置
export interface NotificationSettings {
  userId: string
  
  // 全局设置
  enabled: boolean                 // 是否启用通知
  quietHours: {                    // 免打扰时间
    enabled: boolean
    startTime: string              // 开始时间 HH:mm
    endTime: string                // 结束时间 HH:mm
  }
  
  // 按类型设置
  typeSettings: {
    [key in NotificationType]: {
      enabled: boolean             // 是否启用
      channels: PushChannel[]      // 推送渠道
      priority: NotificationPriority  // 最低优先级
      sound: boolean               // 是否播放声音
      vibration: boolean           // 是否震动
      showPreview: boolean         // 是否显示预览
    }
  }
  
  // 按渠道设置
  channelSettings: {
    [key in PushChannel]: {
      enabled: boolean             // 是否启用
      config?: any                 // 渠道配置
    }
  }
  
  updatedAt: string
}

// 通知查询参数
export interface NotificationQuery {
  page?: number
  limit?: number
  type?: NotificationType
  priority?: NotificationPriority
  status?: NotificationStatus
  recipientId?: string
  startDate?: string
  endDate?: string
  keyword?: string
  unreadOnly?: boolean
}

// 通知统计
export interface NotificationStats {
  total: number                    // 总数
  unread: number                   // 未读数
  read: number                     // 已读数
  processed: number                // 已处理数
  dismissed: number                // 已忽略数
  
  // 按类型统计
  typeStats: {
    type: NotificationType
    count: number
    unreadCount: number
  }[]
  
  // 按优先级统计
  priorityStats: {
    priority: NotificationPriority
    count: number
    unreadCount: number
  }[]
  
  // 今日统计
  todayStats: {
    received: number               // 今日接收
    processed: number              // 今日处理
  }
}

// 推送消息
export interface PushMessage {
  id: string
  notificationId: string          // 关联通知ID
  channel: PushChannel            // 推送渠道
  recipient: string               // 接收者
  
  // 消息内容
  title: string
  body: string
  icon?: string
  image?: string
  badge?: string
  tag?: string                    // 消息标签
  
  // 推送配置
  requireInteraction?: boolean    // 是否需要用户交互
  silent?: boolean                // 是否静默
  vibrate?: number[]              // 震动模式
  sound?: string                  // 声音文件
  
  // 操作按钮
  actions?: {
    action: string
    title: string
    icon?: string
  }[]
  
  // 数据
  data?: any
  
  // 状态
  status: 'pending' | 'sent' | 'delivered' | 'failed'
  error?: string
  
  // 时间
  createdAt: string
  sentAt?: string
  deliveredAt?: string
}

// WebSocket消息
export interface WebSocketMessage {
  type: 'notification' | 'ping' | 'pong' | 'error'
  data?: any
  timestamp: string
}

// 浏览器通知权限
export interface BrowserNotificationPermission {
  permission: NotificationPermission
  supported: boolean
  requestPermission: () => Promise<NotificationPermission>
}

// 通知模板
export interface NotificationTemplate {
  id: string
  type: NotificationType
  name: string                     // 模板名称
  description: string              // 模板描述
  
  // 模板内容
  titleTemplate: string            // 标题模板
  contentTemplate: string          // 内容模板
  summaryTemplate?: string         // 摘要模板
  
  // 默认配置
  defaultPriority: NotificationPriority
  defaultChannels: PushChannel[]
  defaultActions?: NotificationAction[]
  
  // 变量定义
  variables: {
    name: string
    type: 'string' | 'number' | 'date' | 'boolean'
    required: boolean
    description: string
  }[]
  
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 通知规则
export interface NotificationRule {
  id: string
  name: string                     // 规则名称
  description: string              // 规则描述
  
  // 触发条件
  trigger: {
    type: string                   // 触发类型
    conditions: {                  // 触发条件
      field: string
      operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'contains'
      value: any
    }[]
  }
  
  // 通知配置
  notification: {
    templateId: string             // 模板ID
    priority: NotificationPriority
    channels: PushChannel[]
    delay?: number                 // 延迟发送(秒)
    variables?: Record<string, any> // 变量值
  }
  
  // 限制条件
  limits: {
    maxPerHour?: number            // 每小时最大发送数
    maxPerDay?: number             // 每天最大发送数
    cooldown?: number              // 冷却时间(秒)
  }
  
  isActive: boolean
  createdAt: string
  updatedAt: string
}
