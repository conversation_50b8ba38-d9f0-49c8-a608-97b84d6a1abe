import request from './request'
import type { 
  WorkReminder, 
  ReminderSettings, 
  ReminderTemplate,
  ReminderStatistics,
  ReminderSearchParams,
  ReminderBatchOperation,
  ApiResponse, 
  PaginationResponse 
} from '@/types'

// 获取提醒列表
export const getReminderList = (params: ReminderSearchParams): Promise<ApiResponse<PaginationResponse<WorkReminder>>> => {
  return request.get('/reminders', { params })
}

// 获取提醒统计
export const getReminderStatistics = (params?: {
  period?: string
  groupBy?: string
}): Promise<ApiResponse<ReminderStatistics>> => {
  return request.get('/reminders/statistics', { params })
}

// 创建提醒
export const createReminder = (data: {
  reminderType: number
  title: string
  content: string
  relatedId?: number
  priority: number
  dueTime?: string
}): Promise<ApiResponse<WorkReminder>> => {
  return request.post('/reminders', data)
}

// 批量操作提醒
export const batchOperateReminders = (data: ReminderBatchOperation): Promise<ApiResponse> => {
  return request.put('/reminders/batch', data)
}

// 更新提醒状态
export const updateReminderStatus = (id: number, data: {
  status: number
  note?: string
  estimatedCompletion?: string
}): Promise<ApiResponse<WorkReminder>> => {
  return request.put(`/reminders/${id}/status`, data)
}

// 标记提醒已读
export const markReminderRead = (id: number): Promise<ApiResponse> => {
  return request.put(`/reminders/${id}/read`)
}

// 获取提醒设置
export const getReminderSettings = (): Promise<ApiResponse<ReminderSettings>> => {
  return request.get('/reminders/settings')
}

// 更新提醒设置
export const updateReminderSettings = (data: Partial<ReminderSettings>): Promise<ApiResponse<ReminderSettings>> => {
  return request.put('/reminders/settings', data)
}

// 获取提醒模板
export const getReminderTemplates = (params?: {
  type?: number
}): Promise<ApiResponse<ReminderTemplate[]>> => {
  return request.get('/reminders/templates', { params })
}

// 创建提醒模板
export const createReminderTemplate = (data: {
  templateName: string
  templateType: number
  templateContent: string
  variables?: string[]
}): Promise<ApiResponse<ReminderTemplate>> => {
  return request.post('/reminders/templates', data)
}

// 删除提醒模板
export const deleteReminderTemplate = (id: number): Promise<ApiResponse> => {
  return request.delete(`/reminders/templates/${id}`)
}

// 测试提醒功能
export const testReminder = (data: {
  type: string
  content: string
}): Promise<ApiResponse> => {
  return request.post('/reminders/test', data)
}

// 获取未读提醒数量
export const getUnreadReminderCount = (): Promise<ApiResponse<{ count: number }>> => {
  return request.get('/reminders/unread-count')
}
