# 健康管家管理系统详细需求文档

## 1. 项目概述

### 1.1 项目名称
健康管家管理系统 (Health Butler Management System)

### 1.2 项目背景
随着人们对健康管理需求的不断增长，传统的医疗服务模式已无法满足个性化、持续性的健康管理需求。本系统旨在通过专业管家服务，为会员提供全方位、个性化的健康管理服务，实现医疗资源的优化配置和健康管理的标准化、数字化。

### 1.3 项目目标
- 建立完善的会员健康档案管理体系
- 实现健康数据的持续跟踪和分析
- 提供标准化的健康管理服务流程
- 建立医生-管家-会员的协作服务模式
- 实现服务质量的量化评估和持续改进

### 1.4 系统角色详述

#### 1.4.1 管家角色
**职责范围：**
- 会员健康档案的建立和维护
- 定期上门回访和健康状况评估
- 健康问卷的推送和结果跟踪
- 医生处方的执行和反馈
- 会员健康数据的录入和分析
- 健康商品的推荐和销售跟踪

**权限设置：**
- 查看和编辑分配给自己的会员信息
- 创建和发送问卷
- 录入和查看健康指标数据
- 执行处方单并反馈结果
- 查看个人工作统计数据
- 上传回访照片和记录

#### 1.4.2 会员/用户角色
**基本信息：**
- 个人基础信息（姓名、性别、年龄等）
- 健康状况信息（主诉、病史、过敏史等）
- 联系方式和地址信息
- 家庭成员健康信息

**服务接受：**
- 接收管家的上门回访服务
- 通过微信公众号填写健康问卷
- 接受处方执行服务
- 购买推荐的健康商品

#### 1.4.3 医生角色
**职责范围：**
- 根据会员健康状况开具处方
- 提供专业的健康指导建议
- 审核管家提交的健康数据
- 参与疑难案例的会诊

**系统交互：**
- 通过医生端系统开具电子处方
- 查看管家执行处方的反馈结果
- 接收会员健康数据的异常预警

#### 1.4.4 系统管理员角色
**管理职责：**
- 用户账号和权限管理
- 系统配置和参数设置
- 数据备份和安全管理
- 系统监控和性能优化
- 问卷模板的审核和发布

### 1.5 系统架构设计

#### 1.5.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管家Web端     │    │   微信公众号    │    │   医生端系统    │
│   (PC/移动)     │    │   (会员端)      │    │   (处方开具)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端API服务   │
                    │   (业务逻辑)    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据库服务    │
                    │   (数据存储)    │
                    └─────────────────┘
```

#### 1.5.2 技术栈选择
**前端技术：**
- Vue.js 3.x + TypeScript
- Element Plus UI组件库
- ECharts图表库（健康指标可视化）
- Vue Router（路由管理）
- Pinia（状态管理）

**后端技术：**
- Node.js + Express.js / Java Spring Boot
- JWT身份认证
- Redis缓存
- 文件上传处理

**数据库：**
- MySQL 8.0（主数据库）
- Redis（缓存和会话存储）

**第三方服务：**
- 微信公众号开发接口
- 高德地图API（定位服务）
- 阿里云OSS（文件存储）
- 短信服务（通知提醒）

## 2. 功能需求详述

### 2.1 会员管理模块

#### 2.1.1 会员建档功能
**基本信息录入：**
- 姓名（必填）
- 性别（必填）
- 年龄/出生日期（必填）
- 联系电话（必填）
- 主诉（健康问题描述）
- 头像/照片上传功能
- 身份证号码
- 紧急联系人信息
- 家庭住址
- 建档时间（系统自动记录）
- 建档管家（系统自动关联）

**档案管理：**
- 会员信息编辑和更新
- 档案状态管理（活跃/暂停/注销）
- 会员标签分类
- 档案搜索和筛选功能

#### 2.1.2 回访管理功能
**上门回访记录：**
- 回访时间记录
- GPS定位信息（自动获取）
- 回访地址确认
- 回访情况详细说明（富文本编辑）
- 现场照片上传
- 会员状态评估
- 下次回访计划
- 回访类型分类（定期回访/紧急回访/专项回访）

**回访计划管理：**
- 回访计划制定
- 回访提醒功能
- 回访任务分配
- 回访完成状态跟踪

#### 2.1.3 问卷推送功能
**问卷管理：**
- 从问卷库选择适合的问卷
- 个性化问卷定制
- 问卷推送时间设置
- 推送渠道选择（微信公众号）

**推送执行：**
- 通过微信公众号向指定会员推送问卷
- 推送状态跟踪（已发送/已读/已完成）
- 问卷填写提醒功能
- 问卷结果自动收集

#### 2.1.4 用户信息查看
**会员信息总览：**
- 用户名下所有会员列表
- 会员基本信息快速查看
- 会员服务历史记录
- 会员状态统计

**健康指标管理：**
支持的健康指标：
- 血压（收缩压/舒张压）
- 血糖（空腹/餐后）
- 甘油三酯
- 尿酸
- 胆固醇（总胆固醇/HDL/LDL）

**指标数据功能：**
- 指标数据录入和编辑
- 指标历史数据查看
- 折线图可视化展示
- 指标异常预警
- 指标趋势分析
- 数据导出功能

#### 2.1.5 回访记录管理
- 历史回访记录查询
- 回访记录详情查看
- 回访效果评估
- 回访记录统计分析

#### 2.1.6 购买记录管理
- 会员商品购买历史
- 购买金额统计
- 购买频次分析
- 商品推荐记录
- 订单状态跟踪

### 2.2 问卷管理模块

#### 2.2.1 问卷列表功能
**问卷库管理：**
- 问卷模板创建和编辑
- 问卷分类管理（健康评估/满意度调查/专项调研）
- 问卷状态管理（草稿/发布/停用）
- 问卷使用统计

**问卷内容设计：**
- 多种题型支持（单选/多选/填空/评分）
- 问卷逻辑设置（跳转逻辑/显示条件）
- 问卷预览功能
- 问卷模板复制和修改

#### 2.2.2 问卷记录功能
**填写记录管理：**
- 问卷填写状态跟踪
- 填写结果详细查看
- 填写时间和完成度统计
- 问卷结果分析和报告生成

**数据分析：**
- 问卷结果统计图表
- 答案分布分析
- 趋势变化分析
- 数据导出功能

### 2.3 处方单管理模块

#### 2.3.1 处方单接收
- 从医生端系统接收处方单
- 处方单信息完整性验证
- 处方单状态管理（待执行/执行中/已完成/已取消）
- 处方单优先级设置

#### 2.3.2 处方单执行
**执行流程：**
- 处方单详情查看
- 执行计划制定
- 执行进度跟踪
- 执行结果记录
- 执行照片和说明上传

**药品管理：**
- 药品信息查看
- 用药指导记录
- 用药效果跟踪
- 不良反应记录

### 2.4 统计分析模块

#### 2.4.1 工作量统计
**会员管理统计：**
- 新增会员数量（日/周/月/年）
- 活跃会员统计
- 会员增长趋势图
- 会员地区分布

**商品销售统计：**
- 会员商品购买量统计
- 销售金额统计
- 热销商品排行
- 销售趋势分析

**处方执行统计：**
- 处方执行数量统计
- 处方完成率分析
- 处方类型分布
- 执行效率统计

**问卷统计：**
- 问卷发送数量
- 问卷完成率
- 问卷类型分布
- 问卷效果分析

#### 2.4.2 综合报表
- 管家个人工作报表
- 团队整体绩效报表
- 会员满意度报表
- 健康指标改善报表

### 2.5 回访记录模块

#### 2.5.1 回访记录查询
- 按时间范围查询
- 按会员姓名查询
- 按回访类型筛选
- 按回访结果筛选

#### 2.5.2 回访数据分析
- 回访频次统计
- 回访效果评估
- 回访成本分析
- 回访满意度统计

## 3. 技术需求

### 3.1 系统架构
- **前端**：Vue.js/React + Element UI/Ant Design
- **后端**：Node.js/Java Spring Boot/Python Django
- **数据库**：MySQL/PostgreSQL
- **文件存储**：阿里云OSS/腾讯云COS
- **地图服务**：高德地图/百度地图API
- **微信集成**：微信公众号开发接口

### 3.2 移动端支持
- 响应式设计适配移动设备
- GPS定位功能
- 相机拍照功能
- 微信公众号H5页面

### 3.3 数据安全
- 用户数据加密存储
- 访问权限控制
- 操作日志记录
- 数据备份机制

## 4. 用户体验需求

### 4.1 界面设计
- 简洁直观的操作界面
- 响应式布局设计
- 统一的视觉风格
- 无障碍访问支持

### 4.2 操作体验
- 快速数据录入
- 智能表单验证
- 批量操作支持
- 离线数据同步

## 5. 系统集成需求

### 5.1 微信公众号集成
- 问卷推送功能
- 用户身份验证
- 消息通知功能
- 数据回传机制

### 5.2 医生端系统对接
- 处方单数据接口
- 实时数据同步
- 状态反馈机制
- 异常处理机制

## 6. 项目交付物

### 6.1 系统文件
- 前端应用程序
- 后端API服务
- 数据库设计文档
- 部署配置文件

### 6.2 文档资料
- 需求规格说明书
- 系统设计文档
- 用户操作手册
- 系统维护手册
- API接口文档

## 7. 验收标准

- 所有功能模块正常运行
- 数据准确性和完整性
- 系统性能满足要求
- 用户体验良好
- 安全性测试通过
- 集成接口稳定可靠
