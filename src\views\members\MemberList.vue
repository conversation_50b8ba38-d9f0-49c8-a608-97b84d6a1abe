<template>
  <div class="member-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">会员管理</h1>
        <p class="page-desc">管理和查看所有会员信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="goToCreate">
          新建会员
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section">
      <el-card shadow="never">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、电话"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          
          <el-form-item label="性别">
            <el-select v-model="searchForm.gender" placeholder="全部" clearable style="width: 120px">
              <el-option label="男" :value="1" />
              <el-option label="女" :value="2" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="年龄">
            <el-input
              v-model="searchForm.ageMin"
              placeholder="最小"
              type="number"
              style="width: 80px"
            />
            <span style="margin: 0 8px">-</span>
            <el-input
              v-model="searchForm.ageMax"
              placeholder="最大"
              type="number"
              style="width: 80px"
            />
          </el-form-item>
          
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
              <el-option label="活跃" :value="1" />
              <el-option label="暂停" :value="2" />
              <el-option label="注销" :value="3" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 会员列表 -->
    <div class="table-section">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span>会员列表 ({{ total }})</span>
            <div class="header-actions">
              <el-button size="small" :icon="Download" @click="exportData">
                导出
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          v-loading="loading"
          :data="memberList"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="会员信息" min-width="200">
            <template #default="{ row }">
              <div class="member-info">
                <el-avatar :src="row.avatar" :size="40">
                  {{ row.name.charAt(0) }}
                </el-avatar>
                <div class="info-content">
                  <div class="member-name">{{ row.name }}</div>
                  <div class="member-no">{{ row.memberNo }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="基本信息" min-width="150">
            <template #default="{ row }">
              <div>
                <div>{{ row.gender === 1 ? '男' : '女' }} / {{ row.age }}岁</div>
                <div class="text-secondary">{{ row.phone }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="主诉" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.chiefComplaint }}
            </template>
          </el-table-column>
          
          <el-table-column label="健康标签" min-width="150">
            <template #default="{ row }">
              <el-tag
                v-for="tag in row.healthTags"
                :key="tag"
                size="small"
                class="health-tag"
              >
                {{ tag }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="建档时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button size="small" type="text" @click="viewDetail(row.id)">
                查看
              </el-button>
              <el-button size="small" type="text" @click="editMember(row.id)">
                编辑
              </el-button>
              <el-button size="small" type="text" @click="createVisit(row.id)">
                回访
              </el-button>
              <el-dropdown @command="(command) => handleCommand(command, row)">
                <el-button size="small" type="text">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="health">健康指标</el-dropdown-item>
                    <el-dropdown-item command="questionnaire">发送问卷</el-dropdown-item>
                    <el-dropdown-item command="consultation">咨询记录</el-dropdown-item>
                    <el-dropdown-item divided command="delete" class="danger">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Download, ArrowDown } from '@element-plus/icons-vue'
import { getMemberList, deleteMember } from '@/api/member'
import type { Member, MemberSearchParams } from '@/types/member'
import dayjs from 'dayjs'

const router = useRouter()

const loading = ref(false)
const memberList = ref<Member[]>([])
const selectedMembers = ref<Member[]>([])
const total = ref(0)

const searchForm = reactive({
  keyword: '',
  gender: undefined as number | undefined,
  ageMin: '',
  ageMax: '',
  status: undefined as number | undefined
})

const pagination = reactive({
  page: 1,
  limit: 20
})

// 获取会员列表
const fetchMemberList = async () => {
  loading.value = true
  try {
    const params: MemberSearchParams = {
      ...pagination,
      keyword: searchForm.keyword || undefined,
      gender: searchForm.gender,
      status: searchForm.status,
      ageRange: searchForm.ageMin && searchForm.ageMax 
        ? [Number(searchForm.ageMin), Number(searchForm.ageMax)]
        : undefined
    }
    
    const response = await getMemberList(params)
    memberList.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('获取会员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchMemberList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    gender: undefined,
    ageMin: '',
    ageMax: '',
    status: undefined
  })
  pagination.page = 1
  fetchMemberList()
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchMemberList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchMemberList()
}

// 选择
const handleSelectionChange = (selection: Member[]) => {
  selectedMembers.value = selection
}

// 状态相关
const getStatusType = (status: number) => {
  const types = { 1: 'success', 2: 'warning', 3: 'danger' }
  return types[status as keyof typeof types] || 'info'
}

const getStatusText = (status: number) => {
  const texts = { 1: '活跃', 2: '暂停', 3: '注销' }
  return texts[status as keyof typeof texts] || '未知'
}

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

// 操作方法
const goToCreate = () => {
  router.push('/members/create')
}

const viewDetail = (id: number) => {
  router.push(`/members/${id}`)
}

const editMember = (id: number) => {
  router.push(`/members/${id}/edit`)
}

const createVisit = (id: number) => {
  router.push(`/visits/create?memberId=${id}`)
}

const handleCommand = async (command: string, member: Member) => {
  switch (command) {
    case 'health':
      router.push(`/members/${member.id}/health`)
      break
    case 'questionnaire':
      router.push(`/questionnaires/send?memberId=${member.id}`)
      break
    case 'consultation':
      router.push(`/consultations?memberId=${member.id}`)
      break
    case 'delete':
      await handleDelete(member)
      break
  }
}

const handleDelete = async (member: Member) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除会员 "${member.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteMember(member.id)
    ElMessage.success('删除成功')
    fetchMemberList()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const exportData = () => {
  // 导出功能实现
  ElMessage.info('导出功能开发中...')
}

onMounted(() => {
  fetchMemberList()
})
</script>

<style scoped>
.member-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.page-desc {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.search-section {
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-content {
  flex: 1;
}

.member-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.member-no {
  font-size: 12px;
  color: #999;
}

.text-secondary {
  color: #666;
  font-size: 12px;
}

.health-tag {
  margin-right: 4px;
  margin-bottom: 2px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.danger {
  color: var(--danger-color);
}
</style>
