<template>
  <el-dialog
    v-model="visible"
    title="新建回访记录"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="会员" prop="memberId">
            <el-select
              v-model="form.memberId"
              placeholder="请选择会员"
              filterable
              remote
              :remote-method="searchMembers"
              :loading="memberLoading"
              style="width: 100%"
              @change="handleMemberChange"
            >
              <el-option
                v-for="member in memberOptions"
                :key="member.id"
                :label="member.name"
                :value="member.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="回访类型" prop="visitType">
            <el-select v-model="form.visitType" placeholder="请选择回访类型">
              <el-option label="定期回访" :value="1" />
              <el-option label="紧急回访" :value="2" />
              <el-option label="专项回访" :value="3" />
              <el-option label="随访回访" :value="4" />
              <el-option label="满意度回访" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划回访时间" prop="scheduledAt">
            <el-date-picker
              v-model="form.scheduledAt"
              type="datetime"
              placeholder="请选择回访时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="获取当前位置">
            <el-button 
              type="primary" 
              :loading="locationLoading" 
              @click="getCurrentLocation"
            >
              <el-icon><Location /></el-icon>
              获取位置
            </el-button>
            <span v-if="currentLocation" class="location-info">
              {{ currentLocation.address }}
            </span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="健康状况评估" prop="healthAssessment">
        <el-input
          v-model="form.healthAssessment"
          type="textarea"
          :rows="3"
          placeholder="请详细描述会员的健康状况，包括身体状态、精神状态等"
        />
      </el-form-item>

      <el-form-item label="服务执行情况" prop="serviceExecution">
        <el-input
          v-model="form.serviceExecution"
          type="textarea"
          :rows="3"
          placeholder="请描述本次服务的执行情况，包括完成的服务项目、质量等"
        />
      </el-form-item>

      <el-form-item label="发现的问题" prop="problemsFound">
        <el-input
          v-model="form.problemsFound"
          type="textarea"
          :rows="3"
          placeholder="请记录在回访过程中发现的问题或异常情况"
        />
      </el-form-item>

      <el-form-item label="处理建议" prop="suggestions">
        <el-input
          v-model="form.suggestions"
          type="textarea"
          :rows="3"
          placeholder="请提出针对发现问题的处理建议和改进措施"
        />
      </el-form-item>

      <el-form-item label="下次回访计划" prop="nextVisitPlan">
        <el-input
          v-model="form.nextVisitPlan"
          type="textarea"
          :rows="2"
          placeholder="请制定下次回访的计划和重点关注事项"
        />
      </el-form-item>

      <el-form-item label="满意度评分" prop="satisfactionScore">
        <el-rate
          v-model="form.satisfactionScore"
          :max="5"
          show-text
          :texts="['很差', '较差', '一般', '满意', '很满意']"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="其他需要记录的信息"
        />
      </el-form-item>

      <!-- 现场照片 -->
      <el-form-item label="现场照片">
        <FileUpload
          :multiple="true"
          :limit="10"
          :max-size="5 * 1024 * 1024"
          :allowed-types="['jpg', 'jpeg', 'png']"
          category="visit_images"
          :related-type="'visit'"
          tip-text="上传回访现场照片"
          @success="handleImageSuccess"
          @remove="handleImageRemove"
        />
      </el-form-item>

      <!-- 语音记录 -->
      <el-form-item label="语音记录">
        <FileUpload
          :multiple="true"
          :limit="5"
          :max-size="10 * 1024 * 1024"
          :allowed-types="['mp3', 'wav', 'm4a']"
          category="visit_audios"
          :related-type="'visit'"
          tip-text="上传语音记录文件"
          @success="handleAudioSuccess"
          @remove="handleAudioRemove"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { useVisitStore } from '@/stores/visit'
import { searchMembers as searchMembersApi } from '@/api/member'
import FileUpload from '@/components/FileUpload.vue'
import type { CreateVisitParams, VisitType, LocationInfo } from '@/types/visit'
import type { Member } from '@/types/member'
import type { FileInfo } from '@/types/file'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visitStore = useVisitStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const memberLoading = ref(false)
const locationLoading = ref(false)
const memberOptions = ref<Member[]>([])
const currentLocation = ref<LocationInfo | null>(null)
const visitImages = ref<File[]>([])
const visitAudios = ref<File[]>([])

// 常用标签
const commonTags = ref([
  '健康良好', '需要关注', '服务满意', '有改进空间', 
  '按时服药', '饮食规律', '运动不足', '睡眠质量差',
  '情绪稳定', '家属配合', '环境整洁', '安全隐患'
])

// 表单数据
const form = reactive<CreateVisitParams>({
  memberId: 0,
  visitType: 1 as VisitType,
  scheduledAt: '',
  healthAssessment: '',
  serviceExecution: '',
  problemsFound: '',
  suggestions: '',
  nextVisitPlan: '',
  satisfactionScore: 5,
  images: [],
  audios: [],
  notes: '',
  tags: []
})

// 表单验证规则
const rules: FormRules = {
  memberId: [
    { required: true, message: '请选择会员', trigger: 'change' }
  ],
  visitType: [
    { required: true, message: '请选择回访类型', trigger: 'change' }
  ],
  scheduledAt: [
    { required: true, message: '请选择回访时间', trigger: 'change' }
  ],
  healthAssessment: [
    { required: true, message: '请输入健康状况评估', trigger: 'blur' },
    { min: 10, message: '健康状况评估至少需要10个字符', trigger: 'blur' }
  ],
  serviceExecution: [
    { required: true, message: '请输入服务执行情况', trigger: 'blur' },
    { min: 10, message: '服务执行情况至少需要10个字符', trigger: 'blur' }
  ],
  problemsFound: [
    { required: true, message: '请输入发现的问题', trigger: 'blur' }
  ],
  suggestions: [
    { required: true, message: '请输入处理建议', trigger: 'blur' }
  ],
  nextVisitPlan: [
    { required: true, message: '请输入下次回访计划', trigger: 'blur' }
  ],
  satisfactionScore: [
    { required: true, message: '请选择满意度评分', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 搜索会员
const searchMembers = async (query: string) => {
  if (!query) return
  
  try {
    memberLoading.value = true
    const response = await searchMembersApi({ keyword: query, limit: 20 })
    memberOptions.value = response.list
  } catch (error) {
    console.error('搜索会员失败:', error)
  } finally {
    memberLoading.value = false
  }
}

// 会员选择变化
const handleMemberChange = (memberId: number) => {
  const member = memberOptions.value.find(m => m.id === memberId)
  if (member) {
    // 可以根据会员信息预填充一些内容
  }
}

// 获取当前位置
const getCurrentLocation = async () => {
  try {
    locationLoading.value = true
    const location = await visitStore.getCurrentLocationAction()
    currentLocation.value = location
    form.location = location
    ElMessage.success('位置获取成功')
  } catch (error) {
    console.error('获取位置失败:', error)
    ElMessage.error('获取位置失败，请检查定位权限')
  } finally {
    locationLoading.value = false
  }
}

// 图片上传成功
const handleImageSuccess = (files: FileInfo[]) => {
  // 这里需要将FileInfo转换为File对象，或者调整API接口
  // 暂时使用URL数组
  const newFiles = files.map(file => file.url as any)
  visitImages.value.push(...newFiles)
  form.images = [...visitImages.value]
}

// 图片删除
const handleImageRemove = (fileId: string) => {
  // 根据fileId删除对应的图片
}

// 音频上传成功
const handleAudioSuccess = (files: FileInfo[]) => {
  const newFiles = files.map(file => file.url as any)
  visitAudios.value.push(...newFiles)
  form.audios = [...visitAudios.value]
}

// 音频删除
const handleAudioRemove = (fileId: string) => {
  // 根据fileId删除对应的音频
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 设置位置信息
    if (currentLocation.value) {
      form.location = currentLocation.value
    }
    
    await visitStore.createVisitAction(form)
    
    ElMessage.success('回访记录创建成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('创建回访记录失败:', error)
    ElMessage.error('创建回访记录失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.assign(form, {
    memberId: 0,
    visitType: 1 as VisitType,
    scheduledAt: '',
    healthAssessment: '',
    serviceExecution: '',
    problemsFound: '',
    suggestions: '',
    nextVisitPlan: '',
    satisfactionScore: 5,
    images: [],
    audios: [],
    notes: '',
    tags: []
  })
  
  currentLocation.value = null
  visitImages.value = []
  visitAudios.value = []
  memberOptions.value = []
}
</script>

<style scoped>
.location-info {
  margin-left: 10px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-rate__text) {
  color: var(--el-text-color-regular);
}
</style>
