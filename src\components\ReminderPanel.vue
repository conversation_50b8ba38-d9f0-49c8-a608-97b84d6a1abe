<template>
  <div class="reminder-panel">
    <!-- 头部统计 -->
    <div class="reminder-header">
      <div class="stats-grid">
        <div class="stat-item urgent">
          <div class="stat-icon">🔴</div>
          <div class="stat-info">
            <div class="stat-number">{{ urgentCount }}</div>
            <div class="stat-label">紧急</div>
          </div>
        </div>
        <div class="stat-item important">
          <div class="stat-icon">🟡</div>
          <div class="stat-info">
            <div class="stat-number">{{ importantCount }}</div>
            <div class="stat-label">重要</div>
          </div>
        </div>
        <div class="stat-item normal">
          <div class="stat-icon">🟢</div>
          <div class="stat-info">
            <div class="stat-number">{{ normalCount }}</div>
            <div class="stat-label">普通</div>
          </div>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button size="small" @click="markAllRead">
          全部已读
        </el-button>
        <el-button size="small" type="primary" @click="goToReminderPage">
          查看全部
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="reminder-filters">
      <el-radio-group v-model="activeFilter" size="small">
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="unread">未读</el-radio-button>
        <el-radio-button label="urgent">紧急</el-radio-button>
        <el-radio-button label="today">今日</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 提醒列表 -->
    <div class="reminder-list" v-loading="loading">
      <div
        v-for="reminder in filteredReminders"
        :key="reminder.id"
        class="reminder-item"
        :class="{
          'unread': reminder.status === 0,
          'urgent': reminder.priority === 1,
          'important': reminder.priority === 2
        }"
        @click="handleReminderClick(reminder)"
      >
        <div class="reminder-priority">
          <span class="priority-dot" :class="getPriorityClass(reminder.priority)"></span>
        </div>
        
        <div class="reminder-content">
          <div class="reminder-title">{{ reminder.title }}</div>
          <div class="reminder-desc">{{ reminder.content }}</div>
          <div class="reminder-meta">
            <span class="reminder-time">{{ formatTime(reminder.createdAt) }}</span>
            <span class="reminder-type">{{ getReminderTypeText(reminder.reminderType) }}</span>
          </div>
        </div>

        <div class="reminder-actions">
          <el-button
            v-if="reminder.status === 0"
            size="small"
            type="text"
            @click.stop="markAsRead(reminder.id)"
          >
            标记已读
          </el-button>
          <el-button
            size="small"
            type="text"
            @click.stop="handleProcess(reminder)"
          >
            处理
          </el-button>
        </div>
      </div>

      <div v-if="filteredReminders.length === 0" class="empty-state">
        <el-empty description="暂无提醒" :image-size="80" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useReminderStore } from '@/stores/reminder'
import type { WorkReminder } from '@/types/reminder'
import dayjs from 'dayjs'

const emit = defineEmits<{
  close: []
}>()

const router = useRouter()
const reminderStore = useReminderStore()

const activeFilter = ref('all')
const loading = ref(false)

// 计算属性
const reminders = computed(() => reminderStore.reminders)
const urgentCount = computed(() => reminders.value.filter(r => r.priority === 1 && r.status === 0).length)
const importantCount = computed(() => reminders.value.filter(r => r.priority === 2 && r.status === 0).length)
const normalCount = computed(() => reminders.value.filter(r => r.priority === 3 && r.status === 0).length)

const filteredReminders = computed(() => {
  let filtered = reminders.value

  switch (activeFilter.value) {
    case 'unread':
      filtered = filtered.filter(r => r.status === 0)
      break
    case 'urgent':
      filtered = filtered.filter(r => r.priority === 1)
      break
    case 'today':
      const today = dayjs().format('YYYY-MM-DD')
      filtered = filtered.filter(r => dayjs(r.createdAt).format('YYYY-MM-DD') === today)
      break
  }

  return filtered.slice(0, 20) // 限制显示数量
})

// 方法
const getPriorityClass = (priority: number) => {
  const classes = {
    1: 'urgent',
    2: 'important', 
    3: 'normal',
    4: 'info'
  }
  return classes[priority as keyof typeof classes] || 'normal'
}

const getReminderTypeText = (type: number) => {
  const types = {
    1: '处方单',
    2: '回访',
    3: '订单',
    4: '问卷',
    5: '咨询',
    6: '异常'
  }
  return types[type as keyof typeof types] || '其他'
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const handleReminderClick = (reminder: WorkReminder) => {
  // 根据提醒类型跳转到对应页面
  const routes = {
    1: '/prescriptions', // 处方单
    2: '/visits', // 回访
    3: '/orders', // 订单
    4: '/questionnaires', // 问卷
    5: '/consultations', // 咨询
    6: '/members' // 异常
  }
  
  const route = routes[reminder.reminderType as keyof typeof routes]
  if (route) {
    router.push(route)
    emit('close')
  }
}

const markAsRead = async (id: number) => {
  try {
    await reminderStore.updateReminder(id, { status: 1 })
    ElMessage.success('已标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const markAllRead = async () => {
  try {
    await reminderStore.markAllRead()
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleProcess = (reminder: WorkReminder) => {
  // 跳转到具体处理页面
  handleReminderClick(reminder)
}

const goToReminderPage = () => {
  router.push('/reminders')
  emit('close')
}

onMounted(async () => {
  loading.value = true
  try {
    await reminderStore.fetchReminders({ page: 1, limit: 50 })
  } catch (error) {
    console.error('Failed to fetch reminders:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.reminder-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.reminder-header {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  background: #f8f9fa;
}

.stat-icon {
  font-size: 16px;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.reminder-filters {
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.reminder-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.reminder-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 8px;
}

.reminder-item:hover {
  background: #f8f9fa;
}

.reminder-item.unread {
  background: #f0f9ff;
  border-left: 3px solid var(--primary-color);
}

.reminder-item.urgent {
  border-left-color: var(--danger-color);
}

.reminder-item.important {
  border-left-color: var(--warning-color);
}

.reminder-priority {
  margin-top: 2px;
}

.priority-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.priority-dot.urgent {
  background: var(--danger-color);
}

.priority-dot.important {
  background: var(--warning-color);
}

.priority-dot.normal {
  background: var(--success-color);
}

.priority-dot.info {
  background: var(--info-color);
}

.reminder-content {
  flex: 1;
  min-width: 0;
}

.reminder-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reminder-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reminder-meta {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #999;
}

.reminder-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}
</style>
