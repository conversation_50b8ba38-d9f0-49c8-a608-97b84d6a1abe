import request from './request'
import type { LoginForm, RegisterForm, UserInfo, ApiResponse } from '@/types/user'

// 登录
export const login = (data: LoginForm): Promise<ApiResponse<{ token: string; userInfo: UserInfo }>> => {
  return request.post('/auth/login', data)
}

// 注册
export const register = (data: RegisterForm): Promise<ApiResponse> => {
  return request.post('/auth/register', data)
}

// 登出
export const logout = (): Promise<ApiResponse> => {
  return request.post('/auth/logout')
}

// 获取用户信息
export const getUserInfo = (): Promise<ApiResponse<UserInfo>> => {
  return request.get('/auth/user-info')
}

// 刷新token
export const refreshToken = (): Promise<ApiResponse<{ token: string }>> => {
  return request.post('/auth/refresh-token')
}

// 修改密码
export const changePassword = (data: {
  oldPassword: string
  newPassword: string
}): Promise<ApiResponse> => {
  return request.put('/auth/change-password', data)
}

// 上传头像
export const uploadAvatar = (file: File): Promise<ApiResponse<{ url: string }>> => {
  const formData = new FormData()
  formData.append('avatar', file)
  
  return request.post('/auth/upload-avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
