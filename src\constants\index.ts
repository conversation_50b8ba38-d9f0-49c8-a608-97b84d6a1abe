/**
 * 系统常量定义
 */

// 用户角色
export const USER_ROLES = {
  ADMIN: 'admin',
  BUTLER: 'butler',
  DOCTOR: 'doctor',
  MEMBER: 'member'
} as const

// 性别
export const GENDER = {
  MALE: 1,
  FEMALE: 2
} as const

export const GENDER_OPTIONS = [
  { label: '男', value: GENDER.MALE },
  { label: '女', value: GENDER.FEMALE }
]

// 提醒类型
export const REMINDER_TYPES = {
  PRESCRIPTION: 1,    // 处方单
  VISIT: 2,          // 回访
  ORDER: 3,          // 订单
  QUESTIONNAIRE: 4,  // 问卷
  CONSULTATION: 5,   // 咨询
  ANOMALY: 6         // 异常
} as const

export const REMINDER_TYPE_OPTIONS = [
  { label: '处方单', value: REMINDER_TYPES.PRESCRIPTION },
  { label: '回访', value: REMINDER_TYPES.VISIT },
  { label: '订单', value: REMINDER_TYPES.ORDER },
  { label: '问卷', value: REMINDER_TYPES.QUESTIONNAIRE },
  { label: '咨询', value: REMINDER_TYPES.CONSULTATION },
  { label: '异常', value: REMINDER_TYPES.ANOMALY }
]

// 提醒优先级
export const REMINDER_PRIORITY = {
  URGENT: 1,     // 紧急
  IMPORTANT: 2,  // 重要
  NORMAL: 3,     // 普通
  INFO: 4        // 信息
} as const

export const REMINDER_PRIORITY_OPTIONS = [
  { label: '紧急', value: REMINDER_PRIORITY.URGENT, color: 'danger' },
  { label: '重要', value: REMINDER_PRIORITY.IMPORTANT, color: 'warning' },
  { label: '普通', value: REMINDER_PRIORITY.NORMAL, color: 'success' },
  { label: '信息', value: REMINDER_PRIORITY.INFO, color: 'info' }
]

// 提醒状态
export const REMINDER_STATUS = {
  UNREAD: 0,      // 未读
  READ: 1,        // 已读
  PROCESSING: 2,  // 处理中
  COMPLETED: 3,   // 已完成
  DELAYED: 4      // 已延期
} as const

export const REMINDER_STATUS_OPTIONS = [
  { label: '未读', value: REMINDER_STATUS.UNREAD, color: 'danger' },
  { label: '已读', value: REMINDER_STATUS.read, color: 'info' },
  { label: '处理中', value: REMINDER_STATUS.PROCESSING, color: 'warning' },
  { label: '已完成', value: REMINDER_STATUS.COMPLETED, color: 'success' },
  { label: '已延期', value: REMINDER_STATUS.DELAYED, color: 'info' }
]

// 会员状态
export const MEMBER_STATUS = {
  ACTIVE: 1,    // 活跃
  PAUSED: 2,    // 暂停
  CANCELLED: 3  // 注销
} as const

export const MEMBER_STATUS_OPTIONS = [
  { label: '活跃', value: MEMBER_STATUS.ACTIVE, color: 'success' },
  { label: '暂停', value: MEMBER_STATUS.PAUSED, color: 'warning' },
  { label: '注销', value: MEMBER_STATUS.CANCELLED, color: 'danger' }
]

// 问卷类型
export const QUESTIONNAIRE_TYPES = {
  HEALTH_ASSESSMENT: 'health_assessment',
  SYMPTOM_RECORD: 'symptom_record',
  MEDICATION_FEEDBACK: 'medication_feedback',
  LIFESTYLE: 'lifestyle',
  SATISFACTION: 'satisfaction'
} as const

export const QUESTIONNAIRE_TYPE_OPTIONS = [
  { label: '健康评估', value: QUESTIONNAIRE_TYPES.HEALTH_ASSESSMENT, color: 'primary' },
  { label: '症状记录', value: QUESTIONNAIRE_TYPES.SYMPTOM_RECORD, color: 'warning' },
  { label: '用药反馈', value: QUESTIONNAIRE_TYPES.MEDICATION_FEEDBACK, color: 'success' },
  { label: '生活习惯', value: QUESTIONNAIRE_TYPES.LIFESTYLE, color: 'info' },
  { label: '满意度调查', value: QUESTIONNAIRE_TYPES.SATISFACTION, color: 'danger' }
]

// 问卷状态
export const QUESTIONNAIRE_STATUS = {
  DRAFT: 0,      // 草稿
  PUBLISHED: 1,  // 已发布
  DISABLED: 2    // 已停用
} as const

export const QUESTIONNAIRE_STATUS_OPTIONS = [
  { label: '草稿', value: QUESTIONNAIRE_STATUS.DRAFT, color: 'info' },
  { label: '已发布', value: QUESTIONNAIRE_STATUS.PUBLISHED, color: 'success' },
  { label: '已停用', value: QUESTIONNAIRE_STATUS.DISABLED, color: 'danger' }
]

// 健康标签
export const HEALTH_TAGS = [
  '高血压',
  '糖尿病',
  '心脏病',
  '高血脂',
  '肥胖',
  '失眠',
  '焦虑',
  '关节炎',
  '哮喘',
  '胃病',
  '肾病',
  '甲状腺疾病'
]

// 文件类型
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
}

// 文件大小限制（字节）
export const FILE_SIZE_LIMITS = {
  AVATAR: 5 * 1024 * 1024,      // 5MB
  IMAGE: 10 * 1024 * 1024,      // 10MB
  VIDEO: 100 * 1024 * 1024,     // 100MB
  DOCUMENT: 20 * 1024 * 1024    // 20MB
}

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  PAGE_SIZES: [10, 20, 50, 100]
}

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'health_butler_token',
  USER_INFO: 'health_butler_user_info',
  THEME: 'health_butler_theme',
  LANGUAGE: 'health_butler_language',
  SIDEBAR_COLLAPSED: 'health_butler_sidebar_collapsed'
}

// API响应状态码
export const API_CODES = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
}

// 路由名称
export const ROUTE_NAMES = {
  LOGIN: 'Login',
  DASHBOARD: 'Dashboard',
  MEMBER_LIST: 'MemberList',
  MEMBER_CREATE: 'MemberCreate',
  MEMBER_DETAIL: 'MemberDetail',
  REMINDER_LIST: 'ReminderList',
  QUESTIONNAIRE_LIST: 'QuestionnaireList',
  STATISTICS: 'Statistics'
}

// 菜单权限
export const MENU_PERMISSIONS = {
  DASHBOARD: 'dashboard:view',
  MEMBER_MANAGE: 'member:manage',
  MEMBER_CREATE: 'member:create',
  MEMBER_EDIT: 'member:edit',
  MEMBER_DELETE: 'member:delete',
  REMINDER_MANAGE: 'reminder:manage',
  QUESTIONNAIRE_MANAGE: 'questionnaire:manage',
  STATISTICS_VIEW: 'statistics:view'
}

// 正则表达式
export const REGEX = {
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  ID_CARD: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
}

// 默认头像
export const DEFAULT_AVATAR = '/images/default-avatar.png'

// 系统配置
export const SYSTEM_CONFIG = {
  APP_NAME: '健康管家系统',
  VERSION: '1.0.0',
  COPYRIGHT: '© 2024 健康管家系统. All rights reserved.',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '************'
}
