<template>
  <div class="reminder-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">工作提醒</h1>
        <p class="page-desc">管理和处理所有工作提醒事项</p>
      </div>
      <div class="header-right">
        <el-button :icon="Setting" @click="showSettings">
          提醒设置
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card urgent">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.urgentReminders }}</div>
              <div class="stat-label">🔴 紧急提醒</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card important">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.unreadReminders }}</div>
              <div class="stat-label">🟡 未读提醒</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card total">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalReminders }}</div>
              <div class="stat-label">📋 总提醒数</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card efficiency">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.efficiencyScore }}%</div>
              <div class="stat-label">⚡ 处理效率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和操作 -->
    <div class="filter-section">
      <el-card shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <el-radio-group v-model="activeFilter" @change="handleFilterChange">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="unread">未读</el-radio-button>
              <el-radio-button label="urgent">紧急</el-radio-button>
              <el-radio-button label="important">重要</el-radio-button>
              <el-radio-button label="today">今日</el-radio-button>
            </el-radio-group>
          </div>
          
          <div class="filter-right">
            <el-select v-model="typeFilter" placeholder="提醒类型" clearable style="width: 120px">
              <el-option label="处方单" :value="1" />
              <el-option label="回访" :value="2" />
              <el-option label="订单" :value="3" />
              <el-option label="问卷" :value="4" />
              <el-option label="咨询" :value="5" />
              <el-option label="异常" :value="6" />
            </el-select>
            
            <el-button 
              type="primary" 
              :disabled="selectedReminders.length === 0"
              @click="batchMarkRead"
            >
              批量已读
            </el-button>
            
            <el-button @click="markAllRead">
              全部已读
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 提醒列表 -->
    <div class="list-section">
      <el-card shadow="never">
        <div v-loading="loading" class="reminder-list">
          <div
            v-for="reminder in reminderList"
            :key="reminder.id"
            class="reminder-item"
            :class="{
              'unread': reminder.status === 0,
              'urgent': reminder.priority === 1,
              'important': reminder.priority === 2,
              'selected': selectedReminders.includes(reminder.id)
            }"
          >
            <div class="reminder-checkbox">
              <el-checkbox 
                :model-value="selectedReminders.includes(reminder.id)"
                @change="(checked) => handleSelectReminder(reminder.id, checked)"
              />
            </div>
            
            <div class="reminder-priority">
              <span 
                class="priority-dot" 
                :class="getPriorityClass(reminder.priority)"
              ></span>
            </div>
            
            <div class="reminder-content" @click="handleReminderClick(reminder)">
              <div class="reminder-header">
                <div class="reminder-title">{{ reminder.title }}</div>
                <div class="reminder-meta">
                  <span class="reminder-type">{{ getReminderTypeText(reminder.reminderType) }}</span>
                  <span class="reminder-time">{{ formatTime(reminder.createdAt) }}</span>
                </div>
              </div>
              
              <div class="reminder-desc">{{ reminder.content }}</div>
              
              <div class="reminder-footer">
                <div class="reminder-tags">
                  <el-tag 
                    :type="getPriorityTagType(reminder.priority)" 
                    size="small"
                  >
                    {{ getPriorityText(reminder.priority) }}
                  </el-tag>
                  
                  <el-tag 
                    v-if="reminder.dueTime" 
                    type="warning" 
                    size="small"
                  >
                    {{ formatDueTime(reminder.dueTime) }}
                  </el-tag>
                </div>
                
                <div class="reminder-status">
                  <el-tag 
                    :type="getStatusTagType(reminder.status)" 
                    size="small"
                  >
                    {{ getStatusText(reminder.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
            
            <div class="reminder-actions">
              <el-dropdown @command="(command) => handleCommand(command, reminder)">
                <el-button type="text" :icon="MoreFilled" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      v-if="reminder.status === 0" 
                      command="read"
                    >
                      标记已读
                    </el-dropdown-item>
                    <el-dropdown-item command="process">
                      立即处理
                    </el-dropdown-item>
                    <el-dropdown-item command="delay">
                      延期处理
                    </el-dropdown-item>
                    <el-dropdown-item command="assign">
                      转交他人
                    </el-dropdown-item>
                    <el-dropdown-item divided command="delete" class="danger">
                      删除提醒
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <div v-if="reminderList.length === 0" class="empty-state">
            <el-empty description="暂无提醒" :image-size="120" />
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 提醒设置对话框 -->
    <el-dialog
      v-model="settingsVisible"
      title="提醒设置"
      width="600px"
    >
      <ReminderSettings @close="settingsVisible = false" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, MoreFilled } from '@element-plus/icons-vue'
import { useReminderStore } from '@/stores/reminder'
import ReminderSettings from '@/components/ReminderSettings.vue'
import type { WorkReminder, ReminderSearchParams } from '@/types/reminder'
import dayjs from 'dayjs'

const router = useRouter()
const reminderStore = useReminderStore()

const loading = ref(false)
const settingsVisible = ref(false)
const activeFilter = ref('all')
const typeFilter = ref<number | undefined>()
const selectedReminders = ref<number[]>([])

const pagination = reactive({
  page: 1,
  limit: 20
})

// 计算属性
const reminderList = computed(() => reminderStore.reminders)
const statistics = computed(() => reminderStore.statistics || {
  totalReminders: 0,
  unreadReminders: 0,
  urgentReminders: 0,
  efficiencyScore: 0
})
const total = computed(() => reminderList.value.length)

// 获取提醒列表
const fetchReminders = async () => {
  loading.value = true
  try {
    const params: ReminderSearchParams = {
      ...pagination,
      type: typeFilter.value,
      priority: getFilterPriority(),
      status: getFilterStatus()
    }
    
    await reminderStore.fetchReminders(params)
  } catch (error) {
    ElMessage.error('获取提醒列表失败')
  } finally {
    loading.value = false
  }
}

// 获取筛选优先级
const getFilterPriority = () => {
  switch (activeFilter.value) {
    case 'urgent': return 1
    case 'important': return 2
    default: return undefined
  }
}

// 获取筛选状态
const getFilterStatus = () => {
  switch (activeFilter.value) {
    case 'unread': return 0
    default: return undefined
  }
}

// 筛选变化
const handleFilterChange = () => {
  pagination.page = 1
  fetchReminders()
}

// 选择提醒
const handleSelectReminder = (id: number, checked: boolean) => {
  if (checked) {
    selectedReminders.value.push(id)
  } else {
    const index = selectedReminders.value.indexOf(id)
    if (index > -1) {
      selectedReminders.value.splice(index, 1)
    }
  }
}

// 批量标记已读
const batchMarkRead = async () => {
  try {
    await reminderStore.batchOperate('mark_read', selectedReminders.value)
    selectedReminders.value = []
    ElMessage.success('批量标记已读成功')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 全部标记已读
const markAllRead = async () => {
  try {
    await reminderStore.markAllRead()
    ElMessage.success('全部标记已读成功')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 处理提醒点击
const handleReminderClick = (reminder: WorkReminder) => {
  // 根据提醒类型跳转到对应页面
  const routes = {
    1: '/prescriptions',
    2: '/visits',
    3: '/orders',
    4: '/questionnaires',
    5: '/consultations',
    6: '/members'
  }
  
  const route = routes[reminder.reminderType as keyof typeof routes]
  if (route) {
    router.push(route)
  }
}

// 处理命令
const handleCommand = async (command: string, reminder: WorkReminder) => {
  switch (command) {
    case 'read':
      await reminderStore.updateReminder(reminder.id, { status: 1 })
      ElMessage.success('已标记为已读')
      break
    case 'process':
      handleReminderClick(reminder)
      break
    case 'delay':
      // 延期处理逻辑
      ElMessage.info('延期处理功能开发中...')
      break
    case 'assign':
      // 转交他人逻辑
      ElMessage.info('转交功能开发中...')
      break
    case 'delete':
      await handleDelete(reminder)
      break
  }
}

// 删除提醒
const handleDelete = async (reminder: WorkReminder) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除提醒 "${reminder.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里调用删除API
    ElMessage.success('删除成功')
    fetchReminders()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 工具方法
const getPriorityClass = (priority: number) => {
  const classes = { 1: 'urgent', 2: 'important', 3: 'normal', 4: 'info' }
  return classes[priority as keyof typeof classes] || 'normal'
}

const getPriorityText = (priority: number) => {
  const texts = { 1: '紧急', 2: '重要', 3: '普通', 4: '信息' }
  return texts[priority as keyof typeof texts] || '普通'
}

const getPriorityTagType = (priority: number) => {
  const types = { 1: 'danger', 2: 'warning', 3: 'success', 4: 'info' }
  return types[priority as keyof typeof types] || 'info'
}

const getStatusText = (status: number) => {
  const texts = { 0: '未读', 1: '已读', 2: '处理中', 3: '已完成', 4: '已延期' }
  return texts[status as keyof typeof texts] || '未知'
}

const getStatusTagType = (status: number) => {
  const types = { 0: 'danger', 1: 'info', 2: 'warning', 3: 'success', 4: 'info' }
  return types[status as keyof typeof types] || 'info'
}

const getReminderTypeText = (type: number) => {
  const types = { 1: '处方单', 2: '回访', 3: '订单', 4: '问卷', 5: '咨询', 6: '异常' }
  return types[type as keyof typeof types] || '其他'
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const formatDueTime = (time: string) => {
  const due = dayjs(time)
  const now = dayjs()
  const diff = due.diff(now, 'hour')
  
  if (diff < 0) {
    return '已逾期'
  } else if (diff < 24) {
    return `${diff}小时后到期`
  } else {
    return due.format('MM-DD HH:mm')
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchReminders()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchReminders()
}

// 显示设置
const showSettings = () => {
  settingsVisible.value = true
}

onMounted(() => {
  fetchReminders()
  reminderStore.fetchStatistics()
})
</script>

<style scoped>
.reminder-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.page-desc {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 8px;
}

.stat-card.urgent .stat-number { color: var(--danger-color); }
.stat-card.important .stat-number { color: var(--warning-color); }
.stat-card.total .stat-number { color: var(--primary-color); }
.stat-card.efficiency .stat-number { color: var(--success-color); }

.stat-label {
  font-size: 14px;
  color: #666;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reminder-list {
  min-height: 400px;
}

.reminder-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid transparent;
  margin-bottom: 12px;
  transition: all 0.2s;
  cursor: pointer;
}

.reminder-item:hover {
  background: #f8f9fa;
  border-color: var(--border-color);
}

.reminder-item.unread {
  background: #f0f9ff;
  border-left: 4px solid var(--primary-color);
}

.reminder-item.urgent {
  border-left-color: var(--danger-color);
}

.reminder-item.important {
  border-left-color: var(--warning-color);
}

.reminder-item.selected {
  background: #e6f7ff;
  border-color: var(--primary-color);
}

.reminder-priority {
  margin-top: 4px;
}

.priority-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.priority-dot.urgent { background: var(--danger-color); }
.priority-dot.important { background: var(--warning-color); }
.priority-dot.normal { background: var(--success-color); }
.priority-dot.info { background: var(--info-color); }

.reminder-content {
  flex: 1;
  min-width: 0;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.reminder-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.reminder-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.reminder-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.reminder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reminder-tags {
  display: flex;
  gap: 8px;
}

.reminder-actions {
  margin-top: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.danger {
  color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reminder-list-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-right {
    justify-content: space-between;
  }
  
  .reminder-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .reminder-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
