<template>
  <div class="health-charts">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
        <div class="title-info">
          <h2>健康指标图表</h2>
          <p v-if="memberInfo">{{ memberInfo.name }} 的健康趋势分析</p>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Refresh" @click="refreshData">
          刷新数据
        </el-button>
        <el-button type="success" :icon="Download" @click="exportCharts">
          导出图表
        </el-button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="time-range-selector">
      <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
        <el-radio-button label="7">最近7天</el-radio-button>
        <el-radio-button label="30">最近30天</el-radio-button>
        <el-radio-button label="90">最近3个月</el-radio-button>
        <el-radio-button label="180">最近6个月</el-radio-button>
        <el-radio-button label="365">最近1年</el-radio-button>
      </el-radio-group>
      
      <el-date-picker
        v-model="customDateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        @change="handleCustomDateChange"
        style="margin-left: 20px"
      />
    </div>

    <!-- 指标选择 -->
    <div class="indicator-selector">
      <el-checkbox-group v-model="selectedIndicators" @change="handleIndicatorChange">
        <el-checkbox label="blood_pressure">血压</el-checkbox>
        <el-checkbox label="blood_sugar">血糖</el-checkbox>
        <el-checkbox label="heart_rate">心率</el-checkbox>
        <el-checkbox label="weight">体重</el-checkbox>
        <el-checkbox label="bmi">BMI</el-checkbox>
        <el-checkbox label="temperature">体温</el-checkbox>
        <el-checkbox label="oxygen_saturation">血氧饱和度</el-checkbox>
      </el-checkbox-group>
    </div>

    <!-- 图表容器 -->
    <div class="charts-container">
      <el-row :gutter="20">
        <el-col 
          v-for="indicator in selectedIndicators" 
          :key="indicator" 
          :span="12"
          class="chart-col"
        >
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>{{ getIndicatorName(indicator) }}</span>
                <div class="chart-actions">
                  <el-tooltip content="查看详细数据">
                    <el-button type="text" size="small" @click="viewDetailData(indicator)">
                      <el-icon><View /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="导出数据">
                    <el-button type="text" size="small" @click="exportIndicatorData(indicator)">
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
            </template>
            
            <div class="chart-content">
              <!-- 统计信息 -->
              <div class="chart-stats">
                <div class="stat-item">
                  <span class="stat-label">最新值</span>
                  <span class="stat-value">{{ getLatestValue(indicator) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">平均值</span>
                  <span class="stat-value">{{ getAverageValue(indicator) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">趋势</span>
                  <span class="stat-value" :class="getTrendClass(indicator)">
                    <el-icon>
                      <component :is="getTrendIcon(indicator)" />
                    </el-icon>
                    {{ getTrendText(indicator) }}
                  </span>
                </div>
              </div>
              
              <!-- 图表 -->
              <div 
                :ref="el => setChartRef(indicator, el)"
                class="chart"
                :style="{ height: '300px' }"
              />
              
              <!-- 异常提示 -->
              <div v-if="hasAbnormalData(indicator)" class="abnormal-alert">
                <el-alert
                  title="发现异常数据"
                  type="warning"
                  :closable="false"
                  show-icon
                >
                  <template #default>
                    最近{{ timeRange }}天内有{{ getAbnormalCount(indicator) }}个异常数据点，建议关注
                  </template>
                </el-alert>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-if="selectedIndicators.length === 0" class="empty-state">
      <el-empty description="请选择要查看的健康指标" />
    </div>

    <!-- 数据详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`${getIndicatorName(currentIndicator)}详细数据`"
      width="800px"
    >
      <el-table :data="detailData" border>
        <el-table-column prop="date" label="日期" width="150" />
        <el-table-column prop="value" label="数值" width="100" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.abnormalLevel)">
              {{ getStatusText(row.abnormalLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" show-overflow-tooltip />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  Refresh, 
  Download, 
  View,
  TrendCharts,
  Top,
  Bottom
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useHealthStore } from '@/stores/health'
import { useMemberStore } from '@/stores/member'
import type { HealthIndicatorType, HealthTrend, AbnormalLevel } from '@/types/health'

const route = useRoute()
const router = useRouter()
const healthStore = useHealthStore()
const memberStore = useMemberStore()

// 响应式数据
const memberId = ref(Number(route.params.memberId))
const memberInfo = ref<any>(null)
const timeRange = ref('30')
const customDateRange = ref<[string, string] | null>(null)
const selectedIndicators = ref<HealthIndicatorType[]>(['blood_pressure', 'blood_sugar', 'heart_rate'])
const chartInstances = ref<Map<string, echarts.ECharts>>(new Map())
const chartRefs = ref<Map<string, HTMLElement>>(new Map())
const trendsData = ref<Map<string, HealthTrend>>(new Map())
const showDetailDialog = ref(false)
const currentIndicator = ref<HealthIndicatorType>('blood_pressure')
const detailData = ref<any[]>([])

// 计算属性
const startDate = computed(() => {
  if (customDateRange.value) {
    return customDateRange.value[0]
  }
  const days = parseInt(timeRange.value)
  const date = new Date()
  date.setDate(date.getDate() - days)
  return date.toISOString().split('T')[0]
})

const endDate = computed(() => {
  if (customDateRange.value) {
    return customDateRange.value[1]
  }
  return new Date().toISOString().split('T')[0]
})

// 方法
const goBack = () => {
  router.back()
}

const refreshData = () => {
  fetchData()
}

const exportCharts = () => {
  // 导出所有图表
  ElMessage.info('导出功能开发中')
}

const handleTimeRangeChange = () => {
  customDateRange.value = null
  fetchData()
}

const handleCustomDateChange = () => {
  timeRange.value = ''
  fetchData()
}

const handleIndicatorChange = () => {
  nextTick(() => {
    initCharts()
  })
}

const setChartRef = (indicator: string, el: HTMLElement | null) => {
  if (el) {
    chartRefs.value.set(indicator, el)
  }
}

const fetchData = async () => {
  try {
    // 获取会员信息
    memberInfo.value = await memberStore.fetchMemberDetail(memberId.value)
    
    // 获取各指标的趋势数据
    const days = customDateRange.value ? 
      Math.ceil((new Date(endDate.value).getTime() - new Date(startDate.value).getTime()) / (1000 * 60 * 60 * 24)) :
      parseInt(timeRange.value)
    
    for (const indicator of selectedIndicators.value) {
      try {
        const trend = await healthStore.fetchHealthTrend(memberId.value, indicator, days)
        trendsData.value.set(indicator, trend)
      } catch (error) {
        console.error(`获取${indicator}趋势数据失败:`, error)
      }
    }
    
    // 初始化图表
    nextTick(() => {
      initCharts()
    })
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

const initCharts = () => {
  // 销毁现有图表
  chartInstances.value.forEach(chart => {
    chart.dispose()
  })
  chartInstances.value.clear()
  
  // 为每个选中的指标创建图表
  selectedIndicators.value.forEach(indicator => {
    const chartEl = chartRefs.value.get(indicator)
    if (chartEl) {
      const chart = echarts.init(chartEl)
      chartInstances.value.set(indicator, chart)
      renderChart(indicator, chart)
    }
  })
}

const renderChart = (indicator: HealthIndicatorType, chart: echarts.ECharts) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return
  
  const option = {
    title: {
      text: getIndicatorName(indicator),
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.axisValue}<br/>${data.seriesName}: ${data.value} ${getIndicatorUnit(indicator)}`
      }
    },
    xAxis: {
      type: 'category',
      data: trend.data.map(item => item.date),
      axisLabel: {
        formatter: (value: string) => {
          return new Date(value).toLocaleDateString()
        }
      }
    },
    yAxis: {
      type: 'value',
      name: getIndicatorUnit(indicator),
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [{
      name: getIndicatorName(indicator),
      type: 'line',
      data: trend.data.map(item => ({
        value: typeof item.value === 'string' ? parseFloat(item.value) : item.value,
        itemStyle: {
          color: getPointColor(item.abnormalLevel)
        }
      })),
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: '#409EFF'
      },
      areaStyle: {
        opacity: 0.1,
        color: '#409EFF'
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  chart.setOption(option)
}

const getPointColor = (abnormalLevel: AbnormalLevel) => {
  const colors = ['#67C23A', '#E6A23C', '#E6A23C', '#F56C6C', '#F56C6C']
  return colors[abnormalLevel] || '#409EFF'
}

const viewDetailData = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return
  
  currentIndicator.value = indicator
  detailData.value = trend.data.map(item => ({
    date: new Date(item.date).toLocaleDateString(),
    value: item.value,
    unit: getIndicatorUnit(indicator),
    abnormalLevel: item.abnormalLevel,
    notes: ''
  }))
  showDetailDialog.value = true
}

const exportIndicatorData = (indicator: HealthIndicatorType) => {
  // 导出单个指标数据
  ElMessage.info('导出功能开发中')
}

// 辅助方法
const getIndicatorName = (type: HealthIndicatorType) => {
  const names = {
    blood_pressure: '血压',
    blood_sugar: '血糖',
    blood_lipid: '血脂',
    heart_rate: '心率',
    weight: '体重',
    bmi: 'BMI',
    temperature: '体温',
    oxygen_saturation: '血氧饱和度',
    cholesterol: '胆固醇',
    uric_acid: '尿酸',
    creatinine: '肌酐',
    hemoglobin: '血红蛋白'
  }
  return names[type] || type
}

const getIndicatorUnit = (type: HealthIndicatorType) => {
  const units = {
    blood_pressure: 'mmHg',
    blood_sugar: 'mmol/L',
    blood_lipid: 'mmol/L',
    heart_rate: '次/分',
    weight: 'kg',
    bmi: 'kg/m²',
    temperature: '°C',
    oxygen_saturation: '%',
    cholesterol: 'mmol/L',
    uric_acid: 'μmol/L',
    creatinine: 'μmol/L',
    hemoglobin: 'g/L'
  }
  return units[type] || ''
}

const getLatestValue = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend || trend.data.length === 0) return '-'
  return `${trend.lastValue} ${getIndicatorUnit(indicator)}`
}

const getAverageValue = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return '-'
  return `${trend.avgValue} ${getIndicatorUnit(indicator)}`
}

const getTrendClass = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return ''
  
  return {
    'trend-up': trend.trend === 'up',
    'trend-down': trend.trend === 'down',
    'trend-stable': trend.trend === 'stable'
  }
}

const getTrendIcon = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return TrendCharts
  
  switch (trend.trend) {
    case 'up': return Top
    case 'down': return Bottom
    default: return TrendCharts
  }
}

const getTrendText = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return '稳定'
  
  const texts = {
    up: '上升',
    down: '下降',
    stable: '稳定'
  }
  return texts[trend.trend] || '稳定'
}

const hasAbnormalData = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return false
  return trend.data.some(item => item.abnormalLevel > 0)
}

const getAbnormalCount = (indicator: HealthIndicatorType) => {
  const trend = trendsData.value.get(indicator)
  if (!trend) return 0
  return trend.data.filter(item => item.abnormalLevel > 0).length
}

const getStatusType = (level: AbnormalLevel) => {
  const types = ['success', 'warning', 'warning', 'danger', 'danger']
  return types[level] || 'info'
}

const getStatusText = (level: AbnormalLevel) => {
  const texts = ['正常', '轻度异常', '中度异常', '重度异常', '危急']
  return texts[level] || '未知'
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.health-charts {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.title-info h2 {
  margin: 0 0 5px 0;
  color: var(--el-text-color-primary);
}

.title-info p {
  margin: 0;
  color: var(--el-text-color-secondary);
}

.header-right {
  display: flex;
  gap: 10px;
}

.time-range-selector {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.indicator-selector {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.charts-container {
  margin-bottom: 20px;
}

.chart-col {
  margin-bottom: 20px;
}

.chart-card {
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-actions {
  display: flex;
  gap: 5px;
}

.chart-content {
  padding: 0;
}

.chart-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding: 15px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 5px;
}

.stat-value {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.trend-up {
  color: var(--el-color-danger);
}

.trend-down {
  color: var(--el-color-success);
}

.trend-stable {
  color: var(--el-color-info);
}

.chart {
  margin-bottom: 15px;
}

.abnormal-alert {
  margin-top: 15px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
