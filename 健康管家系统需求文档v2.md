# 健康管家系统需求文档

## 1. 项目概述

### 1.1 项目名称
健康管家管理系统 (Health Butler Management System)

### 1.2 项目背景
构建一个综合性的健康管理服务平台，通过专业管家为会员提供个性化健康管理服务，实现医生-管家-会员的协作服务模式，提升健康管理服务的标准化和数字化水平。

### 1.3 项目目标
- 建立完善的会员健康档案管理体系
- 实现健康数据的持续跟踪和可视化分析
- 提供标准化的健康管理服务流程
- 建立高效的医疗资源配置和服务执行机制
- 实现服务质量的量化评估和数据驱动决策

### 1.4 系统角色
- **管家**：核心服务提供者，负责会员管理、回访、问卷推送、处方执行
- **会员**：服务接受者，通过微信公众号参与问卷填写
- **医生**：处方开具者，通过医生端系统提供处方单
- **系统管理员**：系统维护和配置管理

## 2. 详细功能需求

### 2.1 会员管理模块

#### 2.1.1 会员建档功能

**基本信息管理：**
- 姓名（必填，2-20个字符）
- 性别（必填，男/女/其他）
- 年龄（必填，1-150岁整数）
- 联系电话（必填，11位手机号格式验证）
- 主诉（健康问题描述，支持富文本编辑）
- 身份证号（可选，18位格式验证）
- 紧急联系人信息
- 家庭住址（支持地图选择）

**多媒体文件上传：**
- 头像照片上传（jpg/png/gif，单张≤5MB）
- 相关图片上传（病历、检查报告等，支持多张）
- 视频文件上传（mp4/avi/mov，单个≤50MB）
- 文件预览和管理功能
- 文件分类标签（病历、检查报告、日常记录等）

**档案管理功能：**
- 会员编号自动生成（唯一标识）
- 建档时间和建档管家自动记录
- 会员状态管理（活跃/暂停/注销）
- 会员标签分类（疾病类型、风险等级等）
- 重复会员检测（基于姓名+电话）
- 批量导入功能（Excel模板）

#### 2.1.2 回访管理功能

**上门回访记录：**
- 回访基本信息
  - 回访日期和时间选择
  - 回访类型（定期/紧急/专项/随访/满意度）
  - 回访状态（计划中/进行中/已完成/已取消）

- GPS定位功能
  - 自动获取当前GPS坐标（经度、纬度、精度）
  - 地址逆解析（坐标转详细地址）
  - 定位失败时支持手动输入地址
  - 离线定位数据保存，联网后同步

- 回访内容记录
  - 会员当前健康状况（富文本编辑器）
  - 服务执行情况说明
  - 发现的问题和建议
  - 支持语音转文字功能

- 多媒体记录支持
  - 现场拍照功能（支持多张照片）
  - 图片添加文字说明和标注
  - 照片自动添加时间和位置水印
  - 支持录制语音备注

- 回访计划管理
  - 下次回访计划制定
  - 回访提醒设置
  - 回访重点记录
  - 回访任务分配和跟踪

#### 2.1.3 问卷推送功能

**问卷管理：**
- 从问卷库选择适合的问卷模板
- 支持问卷个性化定制和编辑
- 问卷适用人群设置（基于疾病类型、年龄等）
- 问卷有效期和提醒频率设置

**微信公众号推送：**
- 选择目标会员（支持单选、多选、批量选择）
- 个性化推送消息模板
- 推送时间设置（立即推送/定时推送）
- 推送状态实时跟踪
  - 已发送：消息成功推送到微信
  - 已读：会员查看了推送消息
  - 进行中：会员开始填写问卷
  - 已完成：问卷填写完成并提交
  - 已过期：超过有效期未完成
  - 已取消：管家主动取消推送

**问卷链接管理：**
- 为每个会员生成唯一问卷链接
- 支持问卷进度保存和断点续填
- 问卷结果自动回传系统
- 填写提醒和催办功能

#### 2.1.4 用户信息查看功能

**会员信息总览：**
- 用户名下所有会员列表展示
- 会员基本信息快速查看
- 会员服务历史记录
- 会员健康状态标签显示
- 支持按姓名、电话、状态等条件搜索筛选

**健康指标管理：**
支持的健康指标类型：
- 血压指标（收缩压、舒张压，单位mmHg）
- 血糖指标（空腹血糖、餐后血糖，单位mmol/L）
- 血脂指标
  - 总胆固醇（单位mmol/L）
  - 甘油三酯（单位mmol/L）
  - 高密度脂蛋白胆固醇
  - 低密度脂蛋白胆固醇
- 尿酸（单位μmol/L）
- 其他指标（体重、身高、BMI等）

**指标数据功能：**
- 指标数据录入和批量导入
- 历史数据查询和编辑
- 指标正常范围设置和异常预警
- 数据导出功能（Excel/PDF）

**折线图可视化：**
- 多指标同时展示的折线图
- 时间范围选择（最近1个月/3个月/6个月/1年/自定义）
- 指标趋势分析和健康评估
- 异常数据点标注和说明
- 图表导出和打印功能
- 支持与医生分享图表数据

#### 2.1.5 回访记录管理
- 历史回访记录查询（按时间、会员、类型筛选）
- 回访记录详情查看（包含文字、图片、定位信息）
- 回访效果评估和满意度统计
- 回访记录导出功能
- 回访数据统计分析

#### 2.1.6 购买记录管理
- 会员商品购买历史记录
- 订单详情查看（商品、金额、状态、物流）
- 购买统计分析（金额、频次、商品类型）
- 商品推荐记录和效果跟踪
- 购买数据导出功能

### 2.2 问卷管理模块

#### 2.2.1 问卷列表功能

**问卷库管理：**
- 问卷模板创建和编辑
- 问卷分类管理（健康评估/满意度调查/专项调研/疾病管理）
- 问卷状态管理（草稿/已发布/已停用）
- 问卷使用统计和效果分析

**问卷设计功能：**
- 多种题型支持
  - 单选题（单项选择）
  - 多选题（多项选择）
  - 填空题（文本输入）
  - 评分题（1-10分评分）
  - 矩阵题（表格形式）
  - 排序题（拖拽排序）
- 问卷逻辑设置（跳转逻辑、显示条件）
- 问卷预览和测试功能
- 问卷模板复制和修改

#### 2.2.2 问卷记录功能

**填写记录管理：**
- 问卷填写状态跟踪
- 填写结果详细查看
- 填写时间和完成度统计
- 问卷答案分析和报告生成

**数据分析功能：**
- 问卷结果统计图表
- 答案分布分析
- 趋势变化分析
- 交叉分析（按年龄、性别、疾病类型等）
- 数据导出功能（Excel/PDF）

### 2.3 处方单管理模块

#### 2.3.1 处方单接收功能
- 从医生端系统自动接收处方单
- 处方单信息完整性验证
- 处方单状态管理（待执行/执行中/已完成/已取消/已过期）
- 处方单优先级设置（紧急/普通/常规）
- 处方单自动分配给管家

#### 2.3.2 处方单执行功能
- 处方单详情查看（患者信息、药品清单、医嘱说明）
- 执行计划制定和进度跟踪
- 执行过程记录（用药指导、患者反馈）
- 执行结果上传（照片、说明、效果评估）
- 异常情况处理和医生反馈
- 处方完成确认和归档

### 2.4 统计分析模块

#### 2.4.1 工作量统计功能

**会员管理统计：**
- 新增会员数量统计（日/周/月/季/年）
- 活跃会员数量和增长趋势
- 会员地区分布和年龄结构
- 会员健康状况分布统计

**商品销售统计：**
- 会员商品购买量统计
- 销售金额和增长趋势
- 热销商品排行榜
- 客单价和复购率分析
- 销售转化率统计

**处方执行统计：**
- 处方接收和执行数量
- 处方完成率和及时率
- 处方类型分布统计
- 执行效率和质量评估

**问卷统计：**
- 问卷发送数量和完成率
- 问卷类型分布和使用频率
- 问卷填写时长统计
- 问卷效果和满意度分析

**Excel导出功能：**
- 支持所有统计数据的Excel导出
- 自定义导出字段和时间范围
- 导出数据格式化和图表生成
- 定期自动生成统计报表

#### 2.4.2 综合报表功能
- 管家个人工作绩效报表
- 团队整体工作量统计
- 会员满意度综合报表
- 健康指标改善效果报表
- 服务质量评估报表

### 2.5 回访记录模块

#### 2.5.1 回访记录查询功能
- 按时间范围查询回访记录
- 按会员姓名和电话查询
- 按回访类型和状态筛选
- 按管家和地区筛选
- 支持关键词全文搜索

#### 2.5.2 回访数据分析功能
- 回访频次和覆盖率统计
- 回访效果评估和满意度分析
- 回访成本和效益分析
- 回访质量评分和改进建议
- 回访数据可视化展示

## 3. 技术需求

### 3.1 系统架构
- **前端框架**：Vue.js 3.x + TypeScript + Element Plus
- **后端框架**：Node.js + Express / Java Spring Boot
- **数据库**：MySQL 8.0 + Redis
- **文件存储**：阿里云OSS / 腾讯云COS
- **地图服务**：高德地图API / 百度地图API
- **图表组件**：ECharts / Chart.js

### 3.2 移动端支持
- 响应式设计适配移动设备
- GPS定位功能集成
- 相机拍照和视频录制
- 微信公众号H5页面开发
- 离线数据存储和同步

### 3.3 数据安全
- 用户数据加密存储
- 访问权限控制和角色管理
- 操作日志记录和审计
- 数据备份和恢复机制
- 敏感信息脱敏处理

### 3.4 系统集成
- 微信公众号开发接口集成
- 医生端系统API对接
- 第三方支付接口集成
- 短信和邮件通知服务
- Excel导入导出功能

## 4. 非功能性需求

### 4.1 性能要求
- 系统响应时间 < 2秒
- 并发用户数支持 > 1000
- 数据库查询优化
- 文件上传下载速度优化

### 4.2 可用性要求
- 系统可用性 > 99.5%
- 7×24小时稳定运行
- 故障恢复时间 < 30分钟
- 数据备份和容灾机制

### 4.3 兼容性要求
- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 支持移动端浏览器
- 微信公众号兼容性
- 不同操作系统兼容

## 5. 项目交付物

### 5.1 系统文件
- 前端应用程序
- 后端API服务
- 数据库设计和初始化脚本
- 部署配置文件和说明

### 5.2 文档资料
- 需求规格说明书
- 系统设计文档
- 数据库设计文档
- API接口文档
- 用户操作手册
- 系统维护手册
- 测试报告

## 6. 验收标准

- 所有功能模块正常运行
- 数据准确性和完整性验证
- 系统性能满足要求
- 用户体验良好
- 安全性测试通过
- 集成接口稳定可靠
- 文档完整准确
