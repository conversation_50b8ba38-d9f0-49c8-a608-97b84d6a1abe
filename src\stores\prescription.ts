/**
 * 处方单状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  Prescription,
  PrescriptionExecution,
  PrescriptionQuery,
  PrescriptionStats,
  PrescriptionStatus,
  PrescriptionPriority
} from '@/types/prescription'
import {
  getPrescriptionList,
  getPrescriptionDetail,
  createPrescription,
  updatePrescription,
  deletePrescription,
  executePrescription,
  getPrescriptionExecutions,
  batchUpdatePrescriptionStatus,
  getPrescriptionStats,
  getExpiringPrescriptions,
  getUrgentPrescriptions
} from '@/api/prescription'

export const usePrescriptionStore = defineStore('prescription', () => {
  // 状态
  const prescriptions = ref<Prescription[]>([])
  const currentPrescription = ref<Prescription | null>(null)
  const executions = ref<PrescriptionExecution[]>([])
  const stats = ref<PrescriptionStats | null>(null)
  const loading = ref(false)
  const total = ref(0)
  const query = ref<PrescriptionQuery>({
    page: 1,
    limit: 20
  })

  // 计算属性
  const pendingPrescriptions = computed(() => 
    prescriptions.value.filter(p => p.status === 1)
  )

  const urgentPrescriptions = computed(() => 
    prescriptions.value.filter(p => p.priority === 4)
  )

  const expiringPrescriptions = computed(() => {
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
    return prescriptions.value.filter(p => {
      const expiresAt = new Date(p.expiresAt)
      return expiresAt <= tomorrow && p.status === 1
    })
  })

  const completionRate = computed(() => {
    if (prescriptions.value.length === 0) return 0
    const completed = prescriptions.value.filter(p => p.status === 3).length
    return Math.round((completed / prescriptions.value.length) * 100)
  })

  // 获取处方单列表
  const fetchPrescriptions = async (params?: PrescriptionQuery) => {
    try {
      loading.value = true
      if (params) {
        query.value = { ...query.value, ...params }
      }
      
      const response = await getPrescriptionList(query.value)
      prescriptions.value = response.list
      total.value = response.total
      
      return response
    } catch (error) {
      console.error('获取处方单列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取处方单详情
  const fetchPrescriptionDetail = async (id: number) => {
    try {
      loading.value = true
      const prescription = await getPrescriptionDetail(id)
      currentPrescription.value = prescription
      return prescription
    } catch (error) {
      console.error('获取处方单详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建处方单
  const createPrescriptionAction = async (data: any) => {
    try {
      const prescription = await createPrescription(data)
      prescriptions.value.unshift(prescription)
      total.value += 1
      return prescription
    } catch (error) {
      console.error('创建处方单失败:', error)
      throw error
    }
  }

  // 更新处方单
  const updatePrescriptionAction = async (id: number, data: any) => {
    try {
      const prescription = await updatePrescription(id, data)
      const index = prescriptions.value.findIndex(p => p.id === id)
      if (index !== -1) {
        prescriptions.value[index] = prescription
      }
      if (currentPrescription.value?.id === id) {
        currentPrescription.value = prescription
      }
      return prescription
    } catch (error) {
      console.error('更新处方单失败:', error)
      throw error
    }
  }

  // 删除处方单
  const deletePrescriptionAction = async (id: number) => {
    try {
      await deletePrescription(id)
      const index = prescriptions.value.findIndex(p => p.id === id)
      if (index !== -1) {
        prescriptions.value.splice(index, 1)
        total.value -= 1
      }
      if (currentPrescription.value?.id === id) {
        currentPrescription.value = null
      }
    } catch (error) {
      console.error('删除处方单失败:', error)
      throw error
    }
  }

  // 执行处方单
  const executePrescriptionAction = async (id: number, data: any) => {
    try {
      const execution = await executePrescription(id, data)
      executions.value.unshift(execution)
      
      // 更新处方单状态
      await updatePrescriptionAction(id, { status: 3 }) // 已完成
      
      return execution
    } catch (error) {
      console.error('执行处方单失败:', error)
      throw error
    }
  }

  // 获取执行记录
  const fetchExecutions = async (prescriptionId: number) => {
    try {
      const executionList = await getPrescriptionExecutions(prescriptionId)
      executions.value = executionList
      return executionList
    } catch (error) {
      console.error('获取执行记录失败:', error)
      throw error
    }
  }

  // 批量更新状态
  const batchUpdateStatus = async (ids: number[], status: PrescriptionStatus) => {
    try {
      await batchUpdatePrescriptionStatus(ids, status)
      
      // 更新本地状态
      prescriptions.value.forEach(p => {
        if (ids.includes(p.id)) {
          p.status = status
        }
      })
    } catch (error) {
      console.error('批量更新状态失败:', error)
      throw error
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const statistics = await getPrescriptionStats()
      stats.value = statistics
      return statistics
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw error
    }
  }

  // 获取即将过期的处方单
  const fetchExpiringPrescriptions = async (hours: number = 24) => {
    try {
      const expiring = await getExpiringPrescriptions(hours)
      return expiring
    } catch (error) {
      console.error('获取即将过期处方单失败:', error)
      throw error
    }
  }

  // 获取紧急处方单
  const fetchUrgentPrescriptions = async () => {
    try {
      const urgent = await getUrgentPrescriptions()
      return urgent
    } catch (error) {
      console.error('获取紧急处方单失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    prescriptions.value = []
    currentPrescription.value = null
    executions.value = []
    stats.value = null
    total.value = 0
    query.value = { page: 1, limit: 20 }
  }

  return {
    // 状态
    prescriptions,
    currentPrescription,
    executions,
    stats,
    loading,
    total,
    query,
    
    // 计算属性
    pendingPrescriptions,
    urgentPrescriptions,
    expiringPrescriptions,
    completionRate,
    
    // 方法
    fetchPrescriptions,
    fetchPrescriptionDetail,
    createPrescriptionAction,
    updatePrescriptionAction,
    deletePrescriptionAction,
    executePrescriptionAction,
    fetchExecutions,
    batchUpdateStatus,
    fetchStats,
    fetchExpiringPrescriptions,
    fetchUrgentPrescriptions,
    resetState
  }
})
